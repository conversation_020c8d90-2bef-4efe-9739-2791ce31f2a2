# AI Card Visit Generator

## 📋 Mô tả dự án

Dự án tạo ảnh AI dollhouse từ thông tin name card và khuôn mặt người dùng, với text được tích hợp trực tiếp vào ảnh như trong `model1.png`.

## 🔄 Quy trình hoạt động

### Bước 1: Upload Name Card (thay thế Camera 1)
- **Thay vì**: Sử dụng camera chuyên dụng để chụp name card
- **Hiện tại**: Import ảnh name card từ laptop
- **Xử lý**: Sử dụng Gemini AI để trích xuất thông tin

### Bước 2: Xá<PERSON> nhận thông tin
- Hiển thị thông tin đã trích xuất
- Người dùng xác nhận thông tin đúng
- Có thể quay lại chỉnh sửa nếu cần

### Bước 3: Chụ<PERSON> khuôn mặt (thay thế Camera 2)
- **Thay vì**: Sử dụng camera chuyên dụng để chụp khuôn mặt
- **Hiện tại**: Sử dụng camera laptop
- **Chỉ thực hiện**: Sau khi đã xác nhận thông tin card đúng

### Bước 4: Tạo ảnh AI
- Kết hợp khuôn mặt + thông tin name card
- Tạo ảnh dollhouse miniature với text tích hợp
- Style giống như `model1.png`

## 🆚 So sánh Gemini vs Tesseract cho OCR

### Gemini AI (Được chọn)
✅ **Ưu điểm:**
- Hiểu context và layout của business card
- Xử lý được nhiều font chữ và ngôn ngữ
- Có thể phân biệt các loại thông tin (tên, chức vụ, công ty...)
- Độ chính xác cao hơn với các card có thiết kế phức tạp

### Tesseract OCR
❌ **Hạn chế:**
- Chỉ đọc text thuần túy, không hiểu context
- Khó xử lý với font chữ đặc biệt
- Cần tiền xử lý ảnh nhiều
- Độ chính xác thấp với layout phức tạp

## 🏗️ Kiến trúc dự án

```
AI_Image_And_Text/
├── app.py                 # Flask main application
├── ocr_processor.py       # Xử lý OCR với Gemini
├── ai_generator.py        # Tạo ảnh AI dollhouse
├── config.py             # Cấu hình API keys
├── requirements.txt      # Dependencies
├── .env                  # Environment variables
├── templates/
│   └── index.html        # Giao diện web
├── static/
│   └── script.js         # JavaScript frontend
├── uploads/              # Thư mục lưu ảnh upload
├── outputs/              # Thư mục lưu ảnh kết quả
├── sessions/             # Thư mục backup
└── model1.png           # Ảnh tham khảo style

```

## 🔧 Thiết lập cho giai đoạn thử nghiệm

### Camera 1 → Import từ laptop
- **Lý do**: Chưa có thiết bị camera chuyên dụng
- **Cách chuyển đổi sau**: Thay endpoint `/upload_card` thành camera capture
- **Code cần thay**: `cardInput.addEventListener('change', uploadCard)` → camera stream

### Camera 2 → Laptop camera
- **Lý do**: Sử dụng webcam có sẵn
- **Cách chuyển đổi sau**: Thay `navigator.mediaDevices.getUserMedia` thành external camera
- **Code cần thay**: Camera device selection trong `startCamera()`

## 🚀 Cài đặt và chạy

1. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

2. **Cấu hình API key:**
- Mở file `.env`
- Thay `GEMINI_API_KEY` bằng key thực từ Google AI Studio

3. **Chạy ứng dụng:**
```bash
python app.py
```

4. **Truy cập:** http://127.0.0.1:5000

## 🎯 Tính năng chính

- ✅ OCR name card bằng Gemini AI
- ✅ Xác nhận thông tin trước khi tiếp tục
- ✅ Capture face từ laptop camera
- ✅ Tạo ảnh AI dollhouse với text tích hợp
- ✅ Style giống model1.png
- ✅ Download kết quả

## 🔄 Chuyển đổi sang thiết bị thật

Khi có thiết bị camera chuyên dụng:

1. **Camera 1 (Name Card):**
   - Thay `uploadCard()` thành camera capture
   - Thêm auto-capture khi detect card

2. **Camera 2 (Face):**
   - Thay camera device trong `getUserMedia()`
   - Cấu hình resolution và quality cao hơn

## 📝 Ghi chú

- Dự án được thiết kế để dễ dàng chuyển đổi từ giai đoạn thử nghiệm sang production
- Code được tổ chức modular để dễ bảo trì
- Gemini API được chọn vì hiệu quả OCR tốt hơn Tesseract
