#!/usr/bin/env python3
"""
AI Card Visit Application - New workflow implementation
Quy trình: Card → Check Info → Confirm → Face → AI Generation
"""

from flask import Flask, render_template, request, jsonify, send_file, session
import os
import json
from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename
from workflow_controller import WorkflowController

app = Flask(__name__)
app.secret_key = 'ai_card_visit_secret_key_2024'

# Configuration
UPLOAD_FOLDER = 'uploads'
SESSIONS_FOLDER = 'sessions'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# Create directories
Path(UPLOAD_FOLDER).mkdir(exist_ok=True)
Path(SESSIONS_FOLDER).mkdir(exist_ok=True)

# Initialize workflow controller
workflow_controller = WorkflowController(mode="testing")
print(f"🔥 Using: Gemini 2.0 Flash Preview Image Generation")

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Main page"""
    return render_template('ai_card_visit.html')

@app.route('/start_session', methods=['POST'])
def start_session():
    """Start new session"""
    try:
        session_id = workflow_controller.start_new_session()
        session['current_session'] = session_id
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'New session started successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/upload_card', methods=['POST'])
def upload_card():
    """Step 1: Upload business card image"""
    try:
        if 'card_image' not in request.files:
            return jsonify({'success': False, 'error': 'No card image provided'})
        
        file = request.files['card_image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})
        
        if file and allowed_file(file.filename):
            # Get current session
            session_id = session.get('current_session')
            if not session_id:
                session_id = workflow_controller.start_new_session()
                session['current_session'] = session_id
            
            # Save uploaded file
            filename = secure_filename(file.filename)
            session_folder = Path(SESSIONS_FOLDER) / session_id
            session_folder.mkdir(exist_ok=True)
            
            card_path = session_folder / "card.jpg"
            file.save(str(card_path))
            
            # Update workflow
            workflow_controller.current_session['session_id'] = session_id
            workflow_controller.current_session['card_path'] = str(card_path)
            
            return jsonify({
                'success': True,
                'card_path': str(card_path),
                'message': 'Card image uploaded successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Invalid file type'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/extract_card_info', methods=['POST'])
def extract_card_info():
    """Step 2: Extract card information"""
    try:
        session_id = session.get('current_session')
        if not session_id or not workflow_controller.current_session['card_path']:
            return jsonify({'success': False, 'error': 'No card image available'})
        
        # Extract information
        card_info = workflow_controller.ocr_service.extract_text_from_card(
            workflow_controller.current_session['card_path']
        )
        
        if card_info:
            workflow_controller.current_session['card_info'] = card_info
            
            # Save to session folder
            session_folder = Path(SESSIONS_FOLDER) / session_id
            info_file = session_folder / "card_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(card_info, f, ensure_ascii=False, indent=2)
            
            return jsonify({
                'success': True,
                'card_info': card_info,
                'message': 'Card information extracted successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to extract card information'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/update_card_info', methods=['POST'])
def update_card_info():
    """Step 3: Update/confirm card information"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})
        
        # Get updated info from request
        updated_info = request.json
        
        # Update workflow
        workflow_controller.current_session['card_info'] = updated_info
        
        # Save updated info
        session_folder = Path(SESSIONS_FOLDER) / session_id
        info_file = session_folder / "card_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(updated_info, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'card_info': updated_info,
            'message': 'Card information updated successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Step 4: Capture face from camera (direct capture)"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})

        # Handle captured image from camera
        if 'face_image' not in request.files:
            return jsonify({'success': False, 'error': 'No face image provided'})

        file = request.files['face_image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file captured'})

        # Save captured face
        face_path = workflow_controller.save_captured_face(file, session_id)

        if face_path:
            return jsonify({
                'success': True,
                'face_path': face_path,
                'message': 'Face captured and saved successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to save captured face'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/generate_ai_image', methods=['POST'])
def generate_ai_image():
    """Step 5: Generate AI image with integrated text"""
    try:
        session_id = session.get('current_session')
        if not session_id:
            return jsonify({'success': False, 'error': 'No active session'})
        
        # Check if we have all required data
        if not workflow_controller.current_session['face_path'] or not workflow_controller.current_session['card_info']:
            return jsonify({'success': False, 'error': 'Missing face photo or card information'})
        
        # Generate AI image
        result = workflow_controller.ai_generator.generate_dollhouse_image(
            workflow_controller.current_session['face_path'],
            workflow_controller.current_session['card_info']
        )
        
        if result.get('success'):
            # Copy generated images to session folder (keep originals in outputs)
            session_folder = Path(SESSIONS_FOLDER) / session_id
            session_folder.mkdir(exist_ok=True)

            import shutil
            session_image_paths = []

            # Copy all generated images to session folder
            image_paths = result.get('image_paths', [result['image_path']])
            print(f"📁 Copying {len(image_paths)} images to session folder...")

            for i, image_path in enumerate(image_paths, 1):
                original_path = Path(image_path)
                if original_path.exists():
                    session_path = session_folder / f"ai_dollhouse_{session_id}_v{i}.png"
                    shutil.copy2(str(original_path), str(session_path))
                    session_image_paths.append(str(session_path))
                    print(f"✅ Copied to session: {session_path}")
                else:
                    print(f"❌ Original image not found: {original_path}")

            # Update result with session paths
            result['session_image_paths'] = session_image_paths
            result['session_primary_path'] = session_image_paths[0] if session_image_paths else result['image_path']

            # Update workflow
            workflow_controller.current_session['ai_result'] = result
            
            # Save session summary
            session_summary = {
                'session_id': session_id,
                'card_path': workflow_controller.current_session['card_path'],
                'face_path': workflow_controller.current_session['face_path'],
                'card_info': workflow_controller.current_session['card_info'],
                'ai_result': result,
                'timestamp': datetime.now().isoformat()
            }
            
            summary_file = session_folder / "session_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(session_summary, f, ensure_ascii=False, indent=2)
            
            return jsonify({
                'success': True,
                'ai_result': result,
                'session_summary': session_summary,
                'message': f'AI images generated successfully ({result.get("variations_count", 1)} variations)',
                'variations_count': result.get('variations_count', 1),
                'image_paths': result.get('image_paths', [result['image_path']])
            })
        else:
            return jsonify({'success': False, 'error': result.get('error', 'AI generation failed')})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download_image/<session_id>')
def download_image(session_id):
    """Download primary generated AI image (backward compatibility)"""
    try:
        session_folder = Path(SESSIONS_FOLDER) / session_id
        # Try new format first
        image_path = session_folder / f"ai_dollhouse_{session_id}_v1.png"
        if not image_path.exists():
            # Fallback to old format
            image_path = session_folder / f"ai_dollhouse_{session_id}.png"

        if image_path.exists():
            return send_file(str(image_path), as_attachment=True)
        else:
            return jsonify({'error': 'Image not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_session_image/<session_id>/<int:image_number>')
def download_session_image(session_id, image_number):
    """Download specific session image by number"""
    try:
        session_folder = Path(SESSIONS_FOLDER) / session_id
        image_path = session_folder / f"ai_dollhouse_{session_id}_v{image_number}.png"

        if image_path.exists():
            return send_file(str(image_path), as_attachment=True)
        else:
            return jsonify({'error': f'Image {image_number} not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get_session_info')
def get_session_info():
    """Get current session information"""
    try:
        session_id = session.get('current_session')
        if session_id:
            return jsonify({
                'success': True,
                'session_info': workflow_controller.get_session_info()
            })
        else:
            return jsonify({'success': False, 'error': 'No active session'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/switch_to_production', methods=['POST'])
def switch_to_production():
    """Switch to production mode"""
    try:
        workflow_controller.switch_to_production_mode()
        return jsonify({
            'success': True,
            'message': 'Switched to production mode with external cameras'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/system_status')
def system_status():
    """Get system status"""
    try:
        camera_status = workflow_controller.camera_manager.check_availability()
        
        return jsonify({
            'success': True,
            'mode': workflow_controller.mode,
            'cameras_available': camera_status,
            'gemini_available': bool(workflow_controller.ocr_service.gemini_key),
            'ai_generator_ready': True
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 Starting AI Card Visit Application")
    print("=" * 50)
    print("🌐 Access: http://localhost:5000")
    print("🧪 Mode: Testing (File import + Webcam)")
    print("🔄 Switch to production mode via /switch_to_production")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
