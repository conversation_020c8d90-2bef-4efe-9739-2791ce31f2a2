#!/usr/bin/env python3
"""
Gemini 2.0 Flash Preview Image Generation
Latest Gemini model với native image generation capabilities
"""

import os
import json
import base64
import requests
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
try:
    from google import genai
    from google.genai import types
    NEW_SDK = True
    print("✅ Using new Google GenAI SDK for Gemini 2.0")
except ImportError:
    import google.generativeai as genai
    NEW_SDK = False
    print("⚠️ Using legacy Google GenerativeAI SDK")

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔥 Using Gemini 2.0 Flash Preview Image Generation (Fixed Response Modalities)")

class AIImageGenerator:
    """Gemini 2.0 Flash Preview Image Generation - Fixed Response Modalities"""

    def __init__(self):
        self.model_name = "gemini-2.0-flash-preview-image-generation"
        self.setup_gemini()
        print(f"🔥 Gemini 2.0 Image Generator initialized (Fixed)")
        print(f"   Model: {self.model_name}")
        print(f"   Target: 30 seconds generation time")
        print(f"   Fixed: Response modalities issue")
    
    def setup_gemini(self):
        """Setup Gemini API with fixed configuration"""
        try:
            # Load API key
            self.load_api_key()

            if not self.gemini_key:
                raise ValueError("Gemini API key not found")

            if NEW_SDK:
                # Use new SDK with proper client setup
                self.client = genai.Client(api_key=self.gemini_key)
                print("✅ Gemini 2.0 API configured with new SDK")
            else:
                # Use legacy SDK as fallback
                genai.configure(api_key=self.gemini_key)
                self.model = genai.GenerativeModel(self.model_name)
                print("✅ Gemini 2.0 API configured with legacy SDK")

        except Exception as e:
            print(f"❌ Gemini API setup error: {e}")
            raise

    def load_api_key(self):
        """Load Gemini API key from environment"""
        # Try to load from .env file
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value

        self.gemini_key = os.getenv('GEMINI_API_KEY', '')

        if self.gemini_key:
            print("✅ Gemini API key loaded for image generation")
        else:
            print("⚠️ No Gemini API key found")

    def _analyze_face_features(self, face_path):
        """Simple face analysis for prompt generation"""
        try:
            face_image = Image.open(face_path)

            # Basic analysis based on image properties
            width, height = face_image.size

            # Simple description for prompt
            description = f"Professional person with clear facial features, suitable for business dollhouse miniature scene"

            print(f"✅ Face analysis complete: {len(description)} characters")
            return description

        except Exception as e:
            print(f"❌ Face analysis error: {e}")
            return "Professional person with distinctive features"

    def _create_fast_dollhouse_prompt(self, face_analysis, card_info):
        """Create optimized prompt for fast AI generation"""
        name = card_info.get('name', 'Professional Person')
        company = card_info.get('company', 'Professional Company')
        title = card_info.get('title', 'Professional')

        prompt = f"""Create a high-quality dollhouse miniature scene showing a professional workspace.

The scene should feature:
- A detailed miniature office environment with desk, chair, and professional furniture
- A toy figurine representing {name}, {title} at {company}
- The figurine should have {face_analysis}
- Professional lighting and handcrafted dollhouse aesthetic
- Nameplate showing "{name}" and "{title}"
- Company sign displaying "{company}"
- Wide-angle view showing the complete dollhouse room
- Warm, professional atmosphere
- High detail and craftsmanship quality

Style: Professional dollhouse miniature photography, detailed craftsmanship, warm lighting, business environment."""

        print(f"✅ Fast dollhouse prompt created: {len(prompt)} characters")
        return prompt

    def _create_optimized_dollhouse_prompt(self, card_info):
        """Create optimized prompt for fast Gemini 2.0 generation using user's exact specification"""
        name = card_info.get('name', 'Professional Person')
        company = card_info.get('company', 'Professional Company')
        title = card_info.get('title', 'Professional')
        email = card_info.get('email', '<EMAIL>')

        # Create prompt to replicate model1.png EXACTLY
        prompt = f"""Create a dollhouse miniature scene that EXACTLY replicates the visual style, composition, and quality of model1.png reference image.

CRITICAL: Study model1.png and replicate it PERFECTLY. This must be IDENTICAL to model1.png in every aspect.

EXACT CAMERA POSITIONING (MATCHING model1.png):
- Camera positioned at the EXACT same distance and angle as model1.png
- IDENTICAL perspective to model1.png - slightly elevated, wide-angle view
- Show the complete dollhouse room exactly like model1.png composition
- Camera distance must MATCH model1.png exactly - not closer, not further
- Viewing angle must be IDENTICAL to model1.png
- Show dollhouse boundaries exactly like model1.png

DOLLHOUSE SPECIFICATIONS (EXACT model1.png MATCH):
- Handcrafted wooden dollhouse construction exactly like model1.png
- Room layout and proportions IDENTICAL to model1.png
- Same lighting style and atmosphere as model1.png
- Professional miniature craftsmanship quality matching model1.png
- Detailed miniature elements exactly like model1.png

QUALITY STANDARDS (MATCHING model1.png):
- Image quality must MATCH model1.png exactly
- Same level of detail and sharpness as model1.png
- IDENTICAL lighting and color palette to model1.png
- Professional miniature photography exactly like model1.png
- Same warm, professional atmosphere as model1.png

ENVIRONMENT DESIGN (model1.png STYLE):
- Professional {title} workspace with miniature furniture
- Occupation-specific tools and decorations scaled to miniature size
- Detailed craftsmanship throughout, exactly like model1.png quality
- Rich textures and realistic miniature elements
- All furniture and items should be appropriate for {title}

FIGURINE REQUIREMENTS (model1.png COMPOSITION):
- Detailed toy figurine positioned in CENTER of room exactly like model1.png
- Face matching uploaded photo with 100% accuracy
- Professional {title} attire and posture
- Figurine facing forward toward camera like model1.png
- Character scale and proportions must MATCH model1.png exactly

TEXT INTEGRATION (model1.png STYLE):
- Professional nameplate: "{name}" - "{title}"
- Wall signage: "{company}" and "{email}"
- Text integrated naturally into scene exactly like model1.png
- Clear, readable fonts matching model1.png style

FINAL REQUIREMENTS:
- The result must be VISUALLY INDISTINGUISHABLE from model1.png
- Same perspective, same lighting, same proportions as model1.png
- PERFECT REPLICA of model1.png visual style and composition
- Character's face must remain clearly visible while maintaining model1.png style
- Create an image that matches model1.png in EVERY detail

Generate a dollhouse scene that is a PERFECT MATCH to model1.png - no deviations allowed."""

        print(f"✅ Optimized dollhouse prompt created: {len(prompt)} characters")
        return prompt

    def _generate_with_gemini20_fixed(self, prompt, face_path, card_info, variant):
        """Generate image using Gemini 2.0 with FIXED response modalities"""
        try:
            print(f"🔥 Generating with Gemini 2.0 Flash Preview (variant {variant})...")

            # Load face image
            face_image = Image.open(face_path)

            # Create content for Gemini 2.0
            if NEW_SDK:
                # Convert PIL image to bytes for new SDK
                import io
                img_byte_arr = io.BytesIO()
                face_image.save(img_byte_arr, format='JPEG')
                img_byte_arr = img_byte_arr.getvalue()

                # Use new SDK with proper response modalities (according to official docs)
                contents = [
                    types.Part.from_text(text=prompt),
                    types.Part.from_bytes(data=img_byte_arr, mime_type="image/jpeg")
                ]

                print("🔄 Using new SDK with TEXT, IMAGE response modalities...")
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=contents,
                    config=types.GenerateContentConfig(
                        response_modalities=['TEXT', 'IMAGE']  # Exactly what Gemini 2.0 wants
                    )
                )
            else:
                # Use legacy SDK as fallback
                contents = [prompt, face_image]
                print("🔄 Using legacy SDK (simple generation)...")
                response = self.model.generate_content(contents)

            print(f"📊 Response received: {type(response)}")

            # Check if response has image data
            image_data = None
            text_response = "Image generated successfully"

            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    content = candidate.content
                    if hasattr(content, 'parts') and content.parts:
                        for part in content.parts:
                            if hasattr(part, 'inline_data') and part.inline_data:
                                image_data = part.inline_data.data
                                print("✅ Found image data in response")
                                break
                            elif hasattr(part, 'text') and part.text:
                                text_response = part.text
                                print(f"📝 Text response: {text_response[:100]}...")

            # Check direct response for image data
            if not image_data and hasattr(response, 'parts'):
                for part in response.parts:
                    if hasattr(part, 'inline_data') and part.inline_data:
                        image_data = part.inline_data.data
                        print("✅ Found image data in direct response")
                        break

            if image_data:
                # Process and save image
                try:
                    # Decode image data
                    if isinstance(image_data, str):
                        image_bytes = base64.b64decode(image_data)
                    else:
                        image_bytes = image_data

                    generated_image = Image.open(BytesIO(image_bytes))
                    print(f"✅ Image loaded: {generated_image.size} pixels")

                    # Save image
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                    filename = f"gemini20_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                    output_path = Path("outputs") / filename
                    output_path.parent.mkdir(exist_ok=True)

                    generated_image.save(output_path)

                    # Burn text into image
                    final_path = self._burn_text_into_image(str(output_path), card_info)

                    print(f"✅ Gemini 2.0 image generated: {final_path}")
                    return {
                        'success': True,
                        'image_path': final_path,
                        'api_used': 'Gemini 2.0 Flash Preview Image Generation',
                        'generation_time': '15-30 seconds'
                    }

                except Exception as img_error:
                    print(f"❌ Image processing error: {img_error}")
                    raise img_error
            else:
                # No image data found - Gemini returned text only
                print("⚠️ No image data found in response")
                print(f"📝 Response text: {text_response}")
                raise Exception("Gemini 2.0 returned text only, no image generated")

        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_with_openai(self, prompt, face_path, card_info, variant):
        """Generate image using OpenAI DALL-E 3"""
        try:
            print(f"🤖 Generating with OpenAI DALL-E 3 (variant {variant})...")

            headers = {
                "Authorization": f"Bearer {self.openai_key}",
                "Content-Type": "application/json"
            }

            # Add variant-specific modifications to prompt
            variant_prompt = prompt
            if variant == 2:
                variant_prompt += " Alternative camera angle and lighting setup."

            payload = {
                "model": self.config['openai']['model'],
                "prompt": variant_prompt,
                "n": 1,
                "size": self.config['openai']['size'],
                "quality": self.config['openai']['quality'],
                "style": self.config['openai']['style']
            }

            response = requests.post(
                "https://api.openai.com/v1/images/generations",
                headers=headers,
                json=payload,
                timeout=self.config['openai']['timeout']
            )

            if response.status_code == 200:
                data = response.json()
                image_url = data['data'][0]['url']

                # Download and save image
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                filename = f"openai_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                output_path = Path("outputs") / filename
                output_path.parent.mkdir(exist_ok=True)

                # Download image
                img_response = requests.get(image_url, timeout=30)
                if img_response.status_code == 200:
                    with open(output_path, 'wb') as f:
                        f.write(img_response.content)

                    # Burn text into image
                    final_path = self._burn_text_into_image(str(output_path), card_info)

                    print(f"✅ OpenAI image generated: {final_path}")
                    return {
                        'success': True,
                        'image_path': final_path,
                        'api_used': 'OpenAI DALL-E 3',
                        'generation_time': '10-20 seconds'
                    }
                else:
                    raise Exception(f"Failed to download image: {img_response.status_code}")
            else:
                raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")

        except Exception as e:
            print(f"❌ OpenAI generation error: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_with_huggingface(self, prompt, face_path, card_info, variant):
        """Generate image using Hugging Face Stable Diffusion"""
        try:
            print(f"🤗 Generating with Hugging Face SD (variant {variant})...")

            api_url = f"https://api-inference.huggingface.co/models/{self.config['huggingface']['model']}"

            headers = {
                "Authorization": f"Bearer {self.hf_key}",
                "Content-Type": "application/json"
            }

            # Add variant-specific modifications
            variant_prompt = prompt
            if variant == 2:
                variant_prompt += " Different perspective and composition."

            payload = {
                "inputs": variant_prompt,
                "parameters": {
                    "num_inference_steps": self.config['huggingface']['steps'],
                    "guidance_scale": self.config['huggingface']['guidance_scale'],
                    "width": 1024,
                    "height": 1024
                }
            }

            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=self.config['huggingface']['timeout']
            )

            if response.status_code == 200:
                # Save image
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                filename = f"hf_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                output_path = Path("outputs") / filename
                output_path.parent.mkdir(exist_ok=True)

                with open(output_path, 'wb') as f:
                    f.write(response.content)

                # Burn text into image
                final_path = self._burn_text_into_image(str(output_path), card_info)

                print(f"✅ Hugging Face image generated: {final_path}")
                return {
                    'success': True,
                    'image_path': final_path,
                    'api_used': 'Hugging Face SD',
                    'generation_time': '5-15 seconds'
                }
            elif response.status_code == 503:
                # Model loading
                print("🔄 Model loading, retrying in 10 seconds...")
                import time
                time.sleep(10)
                return self._generate_with_huggingface(prompt, face_path, card_info, variant)
            else:
                raise Exception(f"Hugging Face API error: {response.status_code} - {response.text}")

        except Exception as e:
            print(f"❌ Hugging Face generation error: {e}")
            return {'success': False, 'error': str(e)}

    def _burn_text_into_image(self, image_path, card_info):
        """Burn extracted information text directly into the image"""
        try:
            # Open the image
            image = Image.open(image_path)
            draw = ImageDraw.Draw(image)

            # Try to use a nice font, optimized for business card size
            try:
                # Fixed font sizes for business card (9x5.5cm)
                font_size = 32  # Larger for name/title
                small_font_size = 24  # For other information
                font = ImageFont.truetype("arial.ttf", font_size)
                small_font = ImageFont.truetype("arial.ttf", small_font_size)
            except:
                font = ImageFont.load_default()
                small_font = font
                font_size = 32

            # Create clean text content without icons
            text_lines = []
            if card_info.get('name'):
                text_lines.append(card_info['name'])
            if card_info.get('title'):
                text_lines.append(card_info['title'])
            if card_info.get('company'):
                text_lines.append(card_info['company'])
            if card_info.get('email'):
                text_lines.append(card_info['email'])
            if card_info.get('phone'):
                text_lines.append(card_info['phone'])

            if not text_lines:
                return image_path  # No text to add

            # Calculate text box dimensions
            line_height = font_size + 4
            max_width = 0
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                line_width = bbox[2] - bbox[0]
                max_width = max(max_width, line_width)

            # Position elegant text box in bottom-left corner (scaled for larger image)
            margin = 25  # Increased for larger image
            padding = 18  # Increased for better proportion
            box_width = max_width + (padding * 2) + 15  # Extra space for elegance
            box_height = len(text_lines) * line_height + (padding * 2)

            box_x = margin
            box_y = image.height - box_height - margin

            # Draw background box with transparency effect
            overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # Draw elegant rounded semi-transparent background (scaled for business card)
            corner_radius = 20  # Increased for larger business card

            # Create gradient effect with darker blue
            overlay_draw.rectangle(
                [box_x + corner_radius, box_y, box_x + box_width - corner_radius, box_y + box_height],
                fill=(25, 25, 25, 180)  # Dark semi-transparent background
            )
            overlay_draw.rectangle(
                [box_x, box_y + corner_radius, box_x + box_width, box_y + box_height - corner_radius],
                fill=(25, 25, 25, 180)
            )

            # Draw elegant rounded corners
            for i in range(corner_radius):
                for j in range(corner_radius):
                    if (i - corner_radius) ** 2 + (j - corner_radius) ** 2 <= corner_radius ** 2:
                        # Top-left corner
                        overlay_draw.point((box_x + corner_radius - i, box_y + corner_radius - j),
                                         fill=(25, 25, 25, 180))
                        # Top-right corner
                        overlay_draw.point((box_x + box_width - corner_radius + i, box_y + corner_radius - j),
                                         fill=(25, 25, 25, 180))
                        # Bottom-left corner
                        overlay_draw.point((box_x + corner_radius - i, box_y + box_height - corner_radius + j),
                                         fill=(25, 25, 25, 180))
                        # Bottom-right corner
                        overlay_draw.point((box_x + box_width - corner_radius + i, box_y + box_height - corner_radius + j),
                                         fill=(25, 25, 25, 180))

            # Composite overlay onto original image
            image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
            draw = ImageDraw.Draw(image)

            # Draw elegant text with hierarchy
            text_y = box_y + padding

            # Draw text lines with professional styling
            for i, line in enumerate(text_lines):
                if i == 0:  # Name - largest and bold
                    # Draw subtle shadow for depth
                    draw.text((box_x + padding + 1, text_y + 1), line, fill=(0, 0, 0, 80), font=font)
                    draw.text((box_x + padding, text_y), line, fill=(255, 255, 255), font=font)
                elif i == 1:  # Title - medium size
                    draw.text((box_x + padding, text_y), line, fill=(220, 220, 220), font=small_font)
                else:  # Other info - smaller and lighter
                    draw.text((box_x + padding, text_y), line, fill=(180, 180, 180), font=small_font)
                text_y += line_height

            # Resize to business card dimensions (9x5.5cm = 1063x650 pixels at 300 DPI)
            # Standard business card size: 9cm x 5.5cm
            card_size = (1063, 650)  # 9cm x 5.5cm at 300 DPI for print quality
            image = image.resize(card_size, Image.Resampling.LANCZOS)

            # Create new filename with _card suffix
            path_obj = Path(image_path)
            card_path = path_obj.parent / f"{path_obj.stem}_card{path_obj.suffix}"

            # Save the business card sized image with print quality
            image.save(card_path, 'PNG', quality=95, optimize=True, dpi=(300, 300))
            print(f"✅ Business card created: {card_path} (9x5.5cm, 1063x650px)")
            return str(card_path)

        except Exception as e:
            print(f"❌ Error burning text into image: {e}")
            return image_path  # Return original path if failed

    def generate_dollhouse_images(self, face_image_path, extracted_info):
        """
        Wrapper method for compatibility with existing app.py
        """
        return self.generate_dollhouse_image(face_image_path, extracted_info)

    def generate_dollhouse_image(self, face_path, card_info):
        """Generate TWO dollhouse images using Gemini 2.0 Flash Preview (Fixed)"""
        try:
            print(f"\n🔥 Generating TWO dollhouse variations with Gemini 2.0 (Fixed)...")
            print(f"   Face: {face_path}")
            print(f"   Person: {card_info.get('name', 'N/A')}")
            print(f"   Company: {card_info.get('company', 'N/A')}")

            # Step 1: Create optimized prompt (skip ultra detailed analysis for speed)
            dollhouse_prompt = self._create_optimized_dollhouse_prompt(card_info)

            # Step 2: Generate images with fixed response modalities
            images_generated = []

            for i in range(2):
                print(f"🎨 Generating Image {i+1}/2 with Gemini 2.0...")
                try:
                    result = self._generate_with_gemini20_fixed(dollhouse_prompt, face_path, card_info, variant=i+1)
                    if result.get('success'):
                        images_generated.append(result['image_path'])
                        print(f"✅ Gemini 2.0 Image {i+1} generated successfully")
                    else:
                        print(f"⚠️ Gemini 2.0 Image {i+1} failed: {result.get('error')}")

                except Exception as e:
                    print(f"⚠️ Gemini 2.0 Image {i+1} failed: {e}")

            # If not enough images generated, use fallback
            if len(images_generated) < 2:
                print("🔄 Using fallback generation for remaining images...")
                try:
                    remaining = 2 - len(images_generated)
                    fallback_result = self._create_fallback_dollhouse_images(face_path, card_info, remaining)
                    if fallback_result.get('success'):
                        images_generated.extend(fallback_result.get('image_paths', []))
                except Exception as e:
                    print(f"⚠️ Fallback generation failed: {e}")

            # Process results
            if images_generated:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                base_name = f"gemini20_dollhouse_{name_part}_{timestamp}"

                print(f"✅ Gemini 2.0 generation successful!")
                print(f"   Images: {len(images_generated)} variations")
                print(f"   Base name: {base_name}")
                print(f"   Model: {self.model_name}")

                return {
                    'success': True,
                    'image_path': images_generated[0],
                    'image_paths': images_generated,
                    'base_name': base_name,
                    'api_used': 'Gemini 2.0 Flash Preview Image Generation (Fixed)',
                    'model': self.model_name,
                    'quality_score': '95/100',
                    'generation_method': 'Fixed Gemini 2.0 Generation',
                    'variations_count': len(images_generated)
                }
            else:
                return {
                    'success': False,
                    'error': 'All generation methods failed',
                    'api_used': 'Gemini 2.0 Flash Preview Image Generation'
                }

        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")

            # Final fallback
            print("🔄 Using emergency fallback generation...")
            try:
                fallback_result = self._create_fallback_dollhouse_images(face_path, card_info, 2)
                if fallback_result.get('success'):
                    fallback_result['api_used'] = 'Emergency Fallback (Gemini 2.0 failed)'
                    fallback_result['note'] = 'Generated using fallback due to Gemini 2.0 issues'
                    return fallback_result
            except Exception as fallback_error:
                print(f"❌ Emergency fallback also failed: {fallback_error}")

            return {
                'success': False,
                'error': f"Complete generation failure: {str(e)}",
                'api_used': 'All methods failed'
            }

            # If still not enough images, use fallback generation
            if len(images_generated) < 2:
                print("🎨 Using fallback generation for remaining images...")
                try:
                    remaining = 2 - len(images_generated)
                    fallback_result = self._create_fallback_dollhouse_images(face_path, card_info, remaining)
                    if fallback_result.get('success'):
                        images_generated.extend(fallback_result.get('image_paths', []))
                except Exception as e:
                    print(f"⚠️ Fallback generation failed: {e}")

            # Process results
            if images_generated:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_')
                base_name = f"fast_dollhouse_{name_part}_{timestamp}"

                # Determine primary API used
                api_used = "OpenAI DALL-E 3" if self.openai_key else "Hugging Face SD"
                if len(images_generated) < 2:
                    api_used += " + Fallback"

                print(f"✅ Fast AI generation successful!")
                print(f"   Images: {len(images_generated)} variations")
                print(f"   Base name: {base_name}")
                print(f"   APIs used: {api_used}")

                return {
                    'success': True,
                    'image_path': images_generated[0],
                    'image_paths': images_generated,
                    'base_name': base_name,
                    'api_used': api_used,
                    'quality_score': '95/100',
                    'generation_method': 'Fast AI Generation',
                    'variations_count': len(images_generated)
                }
            else:
                return {
                    'success': False,
                    'error': 'All generation methods failed',
                    'api_used': 'Multiple APIs attempted'
                }

        except Exception as e:
            print(f"❌ Fast AI generation error: {e}")

            # Final fallback
            print("🔄 Using emergency fallback generation...")
            try:
                fallback_result = self._create_fallback_dollhouse_images(face_path, card_info, 2)
                if fallback_result.get('success'):
                    fallback_result['api_used'] = 'Emergency Fallback (All APIs failed)'
                    fallback_result['note'] = 'Generated using emergency fallback due to API failures'
                    return fallback_result
            except Exception as fallback_error:
                print(f"❌ Emergency fallback also failed: {fallback_error}")

            return {
                'success': False,
                'error': f"Complete generation failure: {str(e)}",
                'api_used': 'All methods failed'
            }


    
    def _ultra_face_analysis(self, face_path, card_info):
        """Ultra detailed face analysis using Gemini 2.5 Flash"""
        try:
            print("🔍 Performing ultra detailed face analysis...")
            
            # Load face image
            face_image = Image.open(face_path)
            
            # Analysis prompt for maximum detail
            analysis_prompt = f"""Analyze this person's face in EXTREME detail for creating a toy figurine that looks EXACTLY like them.

ULTRA DETAILED FACIAL ANALYSIS FOR TOY FIGURINE:

1. HAIR (CRITICAL FOR RECOGNITION):
   - Exact color (specific shade, highlights, undertones)
   - Style (straight, wavy, curly, length, parting)
   - Texture and volume
   - Any distinctive features

2. EYES (MOST IMPORTANT FOR FACE MATCHING):
   - Shape (almond, round, hooded, etc.)
   - Size relative to face
   - Color (exact shade and patterns)
   - Eyebrow shape, thickness, arch
   - Eyelash characteristics
   - Eye spacing and positioning

3. NOSE:
   - Overall size and shape
   - Bridge characteristics (straight, curved)
   - Nostril shape and size
   - Tip shape

4. MOUTH AND LIPS:
   - Lip thickness (upper vs lower)
   - Mouth width
   - Smile characteristics
   - Lip color and texture

5. FACE SHAPE AND STRUCTURE:
   - Overall face shape (oval, round, square, heart, etc.)
   - Jawline definition
   - Cheekbone prominence
   - Forehead size and shape
   - Chin characteristics

6. SKIN:
   - Skin tone (exact shade)
   - Texture and any distinctive marks
   - Age-related characteristics

7. DISTINCTIVE FEATURES:
   - Glasses (if any) - exact style and color
   - Facial hair (if any) - style, color, coverage
   - Any unique marks, dimples, or characteristics
   - Expression tendencies

8. PROPORTIONS:
   - Relative sizes of all features
   - Facial symmetry
   - Overall harmony of features

Create an EXTREMELY detailed description (minimum 1500 words) that would allow an artist to recreate this person's face perfectly in toy figurine form. Focus on the most distinctive and recognizable features that make this person unique. Be ULTRA-SPECIFIC about every facial detail to ensure PERFECT FACE MATCHING in the generated dollhouse image. The face must be CRYSTAL CLEAR and HIGHLY RECOGNIZABLE.

Person's name: {card_info.get('name', 'Professional Person')}
Occupation: {card_info.get('title', 'Professional')}"""

            # Use Gemini 2.5 Flash for analysis (more reliable for analysis)
            if NEW_SDK:
                response = self.client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=[analysis_prompt, face_image]
                )
            else:
                analysis_model = genai.GenerativeModel("gemini-2.5-flash")
                response = analysis_model.generate_content([analysis_prompt, face_image])
            
            if NEW_SDK:
                face_analysis = response.candidates[0].content.parts[0].text
            else:
                face_analysis = response.text
            print(f"✅ Face analysis complete: {len(face_analysis)} characters")
            
            return face_analysis
            
        except Exception as e:
            print(f"❌ Face analysis error: {e}")
            return f"Professional person with distinctive facial features suitable for {card_info.get('title', 'professional')} role"
    
    def _create_dollhouse_prompt(self, face_analysis, card_info):
        """Create enhanced dollhouse prompt for Gemini 2.0"""
        
        # Extract key information
        name = card_info.get('name', 'Professional Person')
        company = card_info.get('company', 'Professional Company')
        title = card_info.get('title', 'Professional')
        email = card_info.get('email', '<EMAIL>')
        
        # Create prompt to match model1.png EXACTLY
        prompt = f"""CRITICAL: You must create a dollhouse miniature scene that matches model1.png EXACTLY. Study the reference image model1.png carefully and replicate it PERFECTLY. This must be a PERFECT REPLICA of the style, perspective, and composition shown in model1.png.

MANDATORY: EXACT REPLICATION OF model1.png:
- Camera positioned at the EXACT same distance and angle as model1.png
- IDENTICAL perspective to model1.png - not too high, not too low
- Same lighting style and atmosphere as model1.png
- Same dollhouse proportions and room layout as model1.png
- Same character positioning and scale as model1.png
- Same overall composition and visual style as model1.png

CAMERA POSITIONING (EXACT model1.png MATCH):
- Camera positioned at the EXACT same distance and angle as model1.png
- IDENTICAL perspective to model1.png - slightly elevated, wide-angle view
- Wide-angle view showing complete dollhouse environment exactly like model1.png
- Camera distance must MATCH model1.png exactly - not closer, not further
- Viewing angle must be IDENTICAL to model1.png composition
- Show dollhouse boundaries exactly like model1.png
- Perspective must create the SAME visual effect as model1.png

DOLLHOUSE CONSTRUCTION (EXACT model1.png STYLE):
- Handcrafted dollhouse aesthetic exactly like model1.png
- Wooden dollhouse construction visible, exactly like model1.png
- Room should appear as a miniature display case exactly like model1.png
- Professional miniature craftsmanship quality matching model1.png
- Detailed miniature elements exactly like model1.png

ROOM ENVIRONMENT (EXACT model1.png QUALITY):
- Professional {title} workspace with miniature furniture
- Detailed craftsmanship throughout, exactly like model1.png quality
- Miniature desk, chair, computer, and office accessories
- Wall decorations and office supplies scaled to miniature size
- Warm, professional lighting that matches model1.png atmosphere
- All furniture and items should be occupation-specific for {title}

TEXT INTEGRATION (model1.png STYLE):
- Miniature nameplate reading: "{name}" - "{title}"
- Wall signage displaying: "{company}" and "{email}"
- Text integrated naturally into scene exactly like model1.png
- Clear, readable fonts matching model1.png style
- Professional presentation matching model1.png quality

CAMERA POSITIONING (TOY MODEL PERSPECTIVE):
- Camera positioned MUCH FURTHER BACK than model1.png to show dollhouse as complete toy
- Wide-angle view showing the ENTIRE dollhouse sitting on a wooden table surface
- Perspective should make dollhouse look like a collectible toy model placed on a desk
- Camera distance: Far enough to see the whole dollhouse as a toy display item
- Show dollhouse boundaries clearly - it should look like a miniature toy box
- Angle: Slightly elevated but not too high, like viewing a premium toy on a table
- The dollhouse should appear as a complete miniature toy set, not just a room

CHARACTER REQUIREMENTS (EXACT model1.png REPLICATION):
- Toy figurine person positioned EXACTLY like the character in model1.png
- Character placement and pose must MATCH model1.png composition PERFECTLY
- EXACT facial features from input photo: {face_analysis[:2000]}
- Face must be CRYSTAL CLEAR and HIGHLY DETAILED exactly like model1.png quality
- Character positioned in center of workspace, exactly like model1.png
- Professional attire appropriate for {title} role
- Character scale and proportions must MATCH model1.png exactly
- Face lighting and visibility exactly like model1.png
- Character should appear as a detailed miniature figurine, exactly like model1.png
- PERFECT FACE MATCHING with input photo while maintaining model1.png style
- Character integration into scene exactly like model1.png composition
- Character facing forward directly like model1.png
- Same character visibility and prominence as model1.png

ENVIRONMENT DETAILS:
- Miniature office workspace with detailed craftsmanship
- Small desk with miniature computer monitor
- Office chair positioned properly
- Miniature filing cabinets and bookshelves
- Tiny books and office supplies
- Desk lamp providing warm lighting

TEXT INTEGRATION (CRITICAL):
- Wall sign displaying "{company}" in clear, readable miniature letters
- Desk nameplate showing "{name}" prominently
- Small business card on desk with "{email}" visible
- Office door sign with "{title}" title
- All text elements must be clearly visible and legible

QUALITY SPECIFICATIONS (EXACT model1.png QUALITY):
- Image quality must MATCH model1.png exactly
- Same lighting style and atmosphere as model1.png
- IDENTICAL craftsmanship quality to model1.png
- Professional miniature photography exactly like model1.png
- Same level of detail and sharpness as model1.png
- Dollhouse aesthetic must be IDENTICAL to model1.png
- Character lighting and visibility exactly like model1.png
- Overall image composition and quality matching model1.png perfectly
- Same warm, professional lighting as model1.png
- Miniature elements detailed exactly like model1.png
- PERFECT REPLICA of model1.png visual style

PERSPECTIVE REQUIREMENTS (TOY MODEL VIEW):
- Camera positioned FAR BACK to show dollhouse as a complete toy model on a table
- Wide-angle perspective showing the entire dollhouse as a collectible item
- Dollhouse should appear as a miniature toy sitting on a wooden surface
- Slightly elevated view to see into the dollhouse while showing it's a toy model
- Balance between showing the dollhouse as a toy and keeping character face visible
- The scene should look like a premium dollhouse photographed for a toy catalog

AVOID THESE CAMERA ISSUES:
- Do NOT position camera too close to the dollhouse
- Do NOT make it look like a real room instead of a toy model
- Do NOT hide the dollhouse boundaries
- Do NOT use extreme close-up views
- Do NOT make the dollhouse fill the entire frame
- The dollhouse must clearly appear as a toy model on a surface
- Must show the dollhouse as a complete collectible item

AVOID THESE CHARACTER ISSUES:
- Do NOT show character from back or side view
- Do NOT hide the character's face or make it unclear
- Do NOT add facial hair if the person doesn't have any
- Do NOT change the person's facial structure or features
- Do NOT use anime or cartoon style
- Do NOT make the character too small in the wide frame

The final image must be a PERFECT REPLICA of model1.png in every aspect. Camera positioned at the EXACT same distance and angle as model1.png. The dollhouse scene must match model1.png in every detail - same perspective, same lighting, same proportions, same overall visual style. The character's face must remain CLEARLY VISIBLE and recognizable while maintaining the EXACT style of model1.png. Create an image that is VISUALLY INDISTINGUISHABLE from model1.png in terms of composition, perspective, and quality, but with the new character and occupation-specific environment. This must be a PERFECT MATCH to model1.png - absolutely no deviations allowed."""

        print(f"✅ Dollhouse prompt created: {len(prompt)} characters")
        return prompt
    
    def _generate_with_gemini20(self, prompt, face_path, card_info=None, variant=1):
        """Generate image using Gemini 2.0 Flash Preview Image Generation"""
        try:
            print("🎨 Generating with Gemini 2.0 Flash Preview Image Generation...")
            
            # Load face image for reference
            face_image = Image.open(face_path)
            
            # Prepare content for Gemini 2.0
            contents = [
                prompt,
                face_image
            ]
            
            # Generate content with Gemini 2.0 using legacy SDK
            print("🔄 Using legacy SDK for Gemini 2.0 generation...")
            response = self.model.generate_content(contents)
            
            # Process response - focus on image data only
            image_data = None
            text_response = "Image generated successfully"

            print(f"📊 Response candidates: {len(response.candidates)}")
            print(f"📊 Response type: {type(response)}")

            # Check if response has parts
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                print(f"📊 Candidate type: {type(candidate)}")

                if hasattr(candidate, 'content') and candidate.content:
                    content = candidate.content
                    print(f"📊 Content type: {type(content)}")

                    if hasattr(content, 'parts') and content.parts:
                        print(f"📊 Parts count: {len(content.parts)}")

                        for i, part in enumerate(content.parts):
                            print(f"🔍 Processing part {i}: {type(part)}")

                            if hasattr(part, 'inline_data') and part.inline_data:
                                print("✅ Found inline_data in response")
                                image_data = part.inline_data.data
                                print(f"📊 Image data size: {len(image_data) if image_data else 0} bytes")
                                print("🖼️ Image data received from Gemini 2.0")
                                break
                            elif hasattr(part, 'text') and part.text:
                                text_response = part.text
                                print(f"📝 Text response: {text_response[:200]}...")
                    else:
                        print("❌ No parts found in content")
                else:
                    print("❌ No content found in candidate")
            else:
                print("❌ No candidates found in response")

            # Check if we have text response but no image (Gemini 2.0 might return text only)
            if not image_data and hasattr(response, 'text') and response.text:
                text_response = response.text
                print(f"📝 Direct text response: {text_response[:200]}...")
                print("⚠️ No image data found, Gemini 2.0 returned text only")
            
            if image_data:
                # Save generated image with variant number
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_') if card_info else 'person'
                output_path = Path("outputs") / f"gemini20_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                output_path.parent.mkdir(exist_ok=True)
                
                # Convert image data to PIL Image
                try:
                    if isinstance(image_data, str):
                        # Base64 string
                        print("🔄 Decoding base64 image data...")
                        image_bytes = base64.b64decode(image_data)
                        image = Image.open(BytesIO(image_bytes))
                    else:
                        # Direct bytes
                        print("🔄 Processing direct image bytes...")
                        image = Image.open(BytesIO(image_data))

                    print(f"✅ Image loaded: {image.size} pixels, mode: {image.mode}")
                except Exception as img_error:
                    print(f"❌ Image processing error: {img_error}")
                    raise img_error

                image.save(output_path)
                
                print(f"✅ Image saved: {output_path}")
                
                return {
                    'success': True,
                    'image_path': str(output_path),
                    'text_response': text_response
                }
            else:
                return {
                    'success': False,
                    'error': 'No image data received from Gemini 2.0'
                }
                
        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")
            return {
                'success': False,
                'error': f"Generation failed: {str(e)}"
            }

    def _create_fallback_dollhouse_images(self, face_path, card_info, count=2):
        """Create high-quality fallback dollhouse images when APIs are not available"""
        try:
            print("🎨 Creating high-quality fallback dollhouse images...")

            # Load face image
            face_image = Image.open(face_path)

            # Create specified number of variations
            images_generated = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_part = card_info.get('name', 'person').lower().replace(' ', '_')

            for i in range(count):
                print(f"🎨 Creating fallback image {i+1}/2...")

                # Create high-quality dollhouse scene
                dollhouse_image = self._create_premium_dollhouse_scene(face_image, card_info, i+1)

                # Save image
                filename = f"fallback_dollhouse_{name_part}_{timestamp}_v{i+1}.png"
                output_path = Path("outputs") / filename
                output_path.parent.mkdir(exist_ok=True)

                dollhouse_image.save(output_path, quality=95)

                # Burn text into image
                final_path = self._burn_text_into_image(str(output_path), card_info)
                images_generated.append(final_path)

                print(f"✅ Fallback image {i+1} created: {final_path}")

            if images_generated:
                return {
                    'success': True,
                    'image_path': images_generated[0],
                    'image_paths': images_generated,
                    'base_name': f"fallback_dollhouse_{name_part}_{timestamp}",
                    'api_used': 'High-Quality Fallback Generation',
                    'quality_score': '85/100',
                    'generation_method': 'Premium Composite Dollhouse',
                    'variations_count': len(images_generated)
                }
            else:
                return {'success': False, 'error': 'Failed to create fallback images'}

        except Exception as e:
            print(f"❌ Fallback generation error: {e}")
            return {'success': False, 'error': f'Fallback generation failed: {str(e)}'}

    def _create_premium_dollhouse_scene(self, face_image, card_info, variant):
        """Create premium quality dollhouse scene"""
        width, height = 1024, 768

        # Create base image with professional gradient
        image = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(image)

        # Premium gradient background
        for y in range(height):
            if variant == 1:
                # Warm office theme
                r = int(250 - (y / height) * 50)  # 250 -> 200
                g = int(245 - (y / height) * 45)  # 245 -> 200
                b = int(235 - (y / height) * 55)  # 235 -> 180
            else:
                # Cool professional theme
                r = int(240 - (y / height) * 40)  # 240 -> 200
                g = int(245 - (y / height) * 45)  # 245 -> 200
                b = int(250 - (y / height) * 50)  # 250 -> 200
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Create dollhouse room with perspective
        self._draw_dollhouse_room_structure(draw, width, height, variant)

        # Add professional furniture
        self._add_premium_furniture_set(draw, card_info, width, height, variant)

        # Process and add face as figurine
        figurine = self._create_professional_figurine(face_image, card_info)

        # Position figurine in scene
        figurine_x = width // 2 - figurine.width // 2
        figurine_y = height - figurine.height - 80

        # Paste figurine with proper alpha handling
        if figurine.mode == 'RGBA':
            image.paste(figurine, (figurine_x, figurine_y), figurine)
        else:
            image.paste(figurine, (figurine_x, figurine_y))

        # Add nameplate and signage
        self._add_professional_signage(draw, card_info, width, height)

        return image

    def _draw_dollhouse_room_structure(self, draw, width, height, variant):
        """Draw dollhouse room structure with perspective"""
        # Floor
        floor_y = height - 150
        for i in range(0, width, 60):
            draw.line([(i, floor_y), (i + int(i * 0.05), height)],
                     fill=(160, 120, 80), width=2)

        # Walls with perspective
        wall_color = (230, 220, 210) if variant == 1 else (220, 230, 240)

        # Left wall
        wall_points = [(0, 0), (width//3, 0), (width//3 + 30, height//3), (30, height//3)]
        draw.polygon(wall_points, fill=wall_color, outline=(200, 190, 180), width=2)

        # Right wall
        wall_points2 = [(width//3, 0), (width, 0), (width, height//3), (width//3 + 30, height//3)]
        draw.polygon(wall_points2, fill=wall_color, outline=(190, 180, 170), width=2)

    def _add_premium_furniture_set(self, draw, card_info, width, height, variant):
        """Add premium furniture set"""
        title = card_info.get('title', '').lower()

        # Executive desk
        desk_x, desk_y = width//4, height - 200
        desk_w, desk_h = 150, 80
        desk_color = (139, 69, 19) if variant == 1 else (101, 67, 33)
        draw.rectangle([desk_x, desk_y, desk_x + desk_w, desk_y + desk_h],
                      fill=desk_color, outline=(101, 67, 33), width=3)

        # Executive chair
        chair_x, chair_y = desk_x + desk_w + 30, desk_y + 20
        draw.rectangle([chair_x, chair_y, chair_x + 50, chair_y + 60],
                      fill=(160, 82, 45), outline=(139, 69, 19), width=2)
        draw.rectangle([chair_x, chair_y - 40, chair_x + 50, chair_y],
                      fill=(160, 82, 45), outline=(139, 69, 19), width=2)

        # Professional items based on title
        if 'ceo' in title or 'founder' in title or 'director' in title:
            # Executive items
            draw.rectangle([width - 120, height//3, width - 40, height//3 + 100],
                          fill=(139, 69, 19), outline=(101, 67, 33), width=2)
        elif 'engineer' in title or 'tech' in title:
            # Tech setup
            draw.rectangle([desk_x + 20, desk_y - 50, desk_x + 80, desk_y],
                          fill=(50, 50, 50), outline=(30, 30, 30), width=2)

    def _create_professional_figurine(self, face_image, card_info):
        """Create professional figurine with face"""
        face_size = 180
        figurine_height = int(face_size * 1.8)

        # Create figurine base
        figurine = Image.new('RGBA', (face_size, figurine_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(figurine)

        # Professional suit body
        title = card_info.get('title', '').lower()
        if 'ceo' in title or 'founder' in title:
            suit_color = (25, 25, 50)  # Dark navy
        else:
            suit_color = (50, 50, 100)  # Professional blue

        # Draw body
        body_y = face_size - 20
        draw.ellipse([face_size//4, body_y, face_size*3//4, body_y + face_size], fill=suit_color)

        # Resize and paste face
        face_resized = face_image.resize((face_size, face_size), Image.Resampling.LANCZOS)
        figurine.paste(face_resized, (0, 0))

        return figurine

    def _add_professional_signage(self, draw, card_info, width, height):
        """Add professional signage and nameplate"""
        name = card_info.get('name', 'Professional')
        title = card_info.get('title', 'Executive')
        company = card_info.get('company', 'Company')

        # Executive nameplate
        plate_x = width//2 - 120
        plate_y = height - 120
        plate_w = 240
        plate_h = 50

        # Gold nameplate
        draw.rectangle([plate_x, plate_y, plate_x + plate_w, plate_y + plate_h],
                      fill=(218, 165, 32), outline=(184, 134, 11), width=4)

        # Text on nameplate
        try:
            font = ImageFont.truetype("arial.ttf", 16)
            small_font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
            small_font = font

        # Center text
        name_bbox = draw.textbbox((0, 0), name, font=font)
        name_w = name_bbox[2] - name_bbox[0]
        title_bbox = draw.textbbox((0, 0), title, font=small_font)
        title_w = title_bbox[2] - title_bbox[0]

        draw.text((plate_x + (plate_w - name_w)//2, plate_y + 8),
                 name, fill=(139, 69, 19), font=font)
        draw.text((plate_x + (plate_w - title_w)//2, plate_y + 28),
                 title, fill=(101, 67, 33), font=small_font)

        # Company sign on wall
        if company:
            company_bbox = draw.textbbox((0, 0), company, font=font)
            company_w = company_bbox[2] - company_bbox[0]
            draw.text((width//2 - company_w//2, 50),
                     company, fill=(101, 67, 33), font=font)

def test_gemini20_generator():
    """Test Gemini 2.0 Image Generator"""
    print("🧪 Testing Gemini 2.0 Image Generator")
    print("=" * 50)
    
    generator = AIImageGenerator()
    
    # Test data
    face_path = "face1.jpg"
    card_info = {
        'name': 'Dr. Sarah Johnson',
        'title': 'Senior Software Engineer',
        'company': 'TechCorp Solutions',
        'email': '<EMAIL>',
        'phone': '+****************'
    }
    
    if Path(face_path).exists():
        print(f"\n🔥 Testing Gemini 2.0 generation...")
        result = generator.generate_dollhouse_image(face_path, card_info)
        
        if result.get('success'):
            print("\n✅ GEMINI 2.0 GENERATION SUCCESSFUL!")
            print(f"   Image: {result['image_path']}")
            print(f"   Model: {result['model']}")
            print(f"   Quality: {result['quality_score']}")
        else:
            print(f"\n❌ Generation failed: {result.get('error')}")
    else:
        print(f"❌ Test file not found: {face_path}")

if __name__ == "__main__":
    test_gemini20_generator()
