"""
AI Image Generator for Dollhouse Miniature Scenes
Sử dụng Gemini 2.0 Flash Preview để tạo ảnh dollhouse từ face và card info
"""

import os
import time
import json
from datetime import datetime
from PIL import Image
import google.generativeai as genai

class AIImageGenerator:
    def __init__(self):
        """Initialize AI Image Generator với Gemini 2.0"""
        try:
            # Cấu hình Gemini API
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                raise ValueError("GEMINI_API_KEY environment variable not set")
            
            genai.configure(api_key=api_key)
            
            # Sử dụng Gemini 2.0 Flash cho image generation
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            print("✅ AI Image Generator initialized with Gemini 2.0 Flash Exp")
            
        except Exception as e:
            print(f"❌ Error initializing AI Image Generator: {e}")
            raise
    
    def create_dollhouse_prompt(self, card_info, face_description=""):
        """Tạo prompt cho dollhouse miniature scene"""
        
        # Extract thông tin từ card
        name = card_info.get('name', 'Professional')
        company = card_info.get('company', 'Company')
        position = card_info.get('title', card_info.get('position', 'Employee'))
        
        # Tạo prompt chi tiết theo yêu cầu
        prompt = f"""Create a dollhouse miniature scene showing a professional work environment. 

SCENE REQUIREMENTS:
- Top-down wide angle view showing 2 wall corners and floor (not too high angle)
- Professional work environment specific to: {position} at {company}
- Full-body toy statue of a person in the middle, facing forward directly
- The person should have realistic facial features matching the reference photo
- Warm, cozy lighting with handmade textures
- Toy box perspective as if the model is placed on a table surface

ENVIRONMENT DETAILS:
- Create a {position} workspace with appropriate furniture and tools
- Include profession-specific items and equipment
- Miniature desk, chair, and work materials
- Small decorative elements that fit the profession

TEXT ELEMENTS:
- Small nameplate on desk showing: "{name}"
- Wall text/sign showing: "{company}"
- Professional and clean typography

STYLE:
- Dollhouse miniature aesthetic
- Handcrafted, detailed textures
- Warm lighting creating cozy atmosphere
- High quality, realistic miniature scene
- Colors should be warm and inviting

The scene should look like a carefully crafted dollhouse room that tells the story of this professional's work life."""

        return prompt
    
    def generate_dollhouse_images(self, face_image_path, card_info, output_folder, sessions_folder, num_images=2):
        """Generate dollhouse images - Mock version for testing"""
        try:
            print(f"🎨 Bắt đầu tạo {num_images} ảnh dollhouse (Mock mode)...")

            # Đọc face image để verify
            if not os.path.exists(face_image_path):
                raise FileNotFoundError(f"❌ Face image not found: {face_image_path}")

            try:
                face_image = Image.open(face_image_path)
                print(f"📷 Loaded face image: {face_image.size}")
            except Exception as e:
                raise ValueError(f"❌ Không thể đọc face image: {e}")

            # Tạo prompt
            prompt = self.create_dollhouse_prompt(card_info)
            print(f"📝 Generated prompt: {len(prompt)} characters")

            generated_images = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Mock generation - tạo ảnh placeholder
            for i in range(num_images):
                try:
                    print(f"🎯 Generating mock image {i+1}/{num_images}...")

                    # Tạo ảnh placeholder với thông tin card
                    mock_image = self.create_mock_dollhouse_image(face_image, card_info, i+1)

                    # Lưu ảnh vào outputs folder
                    output_filename = f"dollhouse_{timestamp}_{i+1}.png"
                    output_path = os.path.join(output_folder, output_filename)
                    mock_image.save(output_path)

                    # Copy vào sessions folder
                    sessions_path = os.path.join(sessions_folder, output_filename)
                    mock_image.save(sessions_path)

                    generated_images.append(output_filename)
                    print(f"✅ Saved mock image {i+1}: {output_filename}")

                    # Delay để simulate processing
                    time.sleep(1)

                except Exception as e:
                    print(f"❌ Error generating mock image {i+1}: {e}")
                    continue

            if generated_images:
                print(f"🎉 Successfully generated {len(generated_images)} mock images")
                return generated_images
            else:
                print("❌ No images were generated")
                return []

        except Exception as e:
            print(f"❌ Error in generate_dollhouse_images: {e}")
            return []

    def create_mock_dollhouse_image(self, face_image, card_info, image_number):
        """Tạo ảnh mock dollhouse để test"""
        from PIL import Image, ImageDraw, ImageFont

        # Tạo canvas 512x512
        width, height = 512, 512
        mock_image = Image.new('RGB', (width, height), color='lightblue')
        draw = ImageDraw.Draw(mock_image)

        try:
            # Sử dụng font mặc định
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        except:
            font_large = None
            font_small = None

        # Vẽ background dollhouse
        draw.rectangle([50, 50, width-50, height-50], fill='lightgray', outline='black', width=3)
        draw.text((width//2-50, 70), f"Dollhouse Scene #{image_number}", fill='black', font=font_large)

        # Resize và paste face image
        try:
            face_resized = face_image.resize((100, 100))
            mock_image.paste(face_resized, (width//2-50, 150))
        except:
            draw.rectangle([width//2-50, 150, width//2+50, 250], fill='pink', outline='black')
            draw.text((width//2-30, 190), "FACE", fill='black', font=font_small)

        # Thêm thông tin card
        y_pos = 280
        for key, value in card_info.items():
            if value and len(value.strip()) > 0:
                text = f"{key}: {value[:20]}"
                draw.text((70, y_pos), text, fill='black', font=font_small)
                y_pos += 25
                if y_pos > height - 100:
                    break

        # Thêm decorative elements
        draw.rectangle([100, 400, 200, 450], fill='brown', outline='black')  # Table
        draw.text((110, 415), "Desk", fill='white', font=font_small)

        draw.rectangle([300, 400, 400, 450], fill='green', outline='black')  # Chair
        draw.text((320, 415), "Chair", fill='white', font=font_small)

        return mock_image
    
    def create_business_card_overlay(self, card_info):
        """Tạo overlay thông tin business card"""
        overlay_info = {
            'name': card_info.get('name', ''),
            'company': card_info.get('company', ''),
            'position': card_info.get('title', card_info.get('position', '')),
            'phone': card_info.get('phone', ''),
            'email': card_info.get('email', '')
        }
        
        # Chỉ giữ thông tin không rỗng
        filtered_info = {k: v for k, v in overlay_info.items() if v}
        
        return filtered_info
    
    def save_generation_metadata(self, card_info, generated_images, sessions_folder):
        """Lưu metadata của generation session"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            metadata = {
                'timestamp': timestamp,
                'card_info': card_info,
                'generated_images': generated_images,
                'generation_time': datetime.now().isoformat()
            }
            
            metadata_path = os.path.join(sessions_folder, f"metadata_{timestamp}.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Saved metadata: {metadata_path}")
            
        except Exception as e:
            print(f"⚠️ Error saving metadata: {e}")

# Test function
if __name__ == "__main__":
    # Test AI Image Generator
    generator = AIImageGenerator()
    
    # Test card info
    test_card_info = {
        'name': 'John Doe',
        'company': 'Tech Corp',
        'position': 'Software Engineer',
        'phone': '******-0123',
        'email': '<EMAIL>'
    }
    
    # Test face image path
    test_face_path = "test_face.jpg"
    
    if os.path.exists(test_face_path):
        result = generator.generate_dollhouse_images(
            face_image_path=test_face_path,
            card_info=test_card_info,
            output_folder="test_outputs",
            sessions_folder="test_sessions"
        )
        print("Test result:", result)
    else:
        print("Test face image not found")
