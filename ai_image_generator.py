"""
AI Image Generator for Dollhouse Miniature Scenes
Sử dụng Gemini 2.0 Flash Preview để tạo ảnh dollhouse từ face và card info
"""

import os
import time
import json
from datetime import datetime
from PIL import Image
import google.generativeai as genai

class AIImageGenerator:
    def __init__(self):
        """Initialize AI Image Generator với Gemini 2.0"""
        try:
            # Cấu hình Gemini API
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                raise ValueError("GEMINI_API_KEY environment variable not set")
            
            genai.configure(api_key=api_key)
            
            # Sử dụng Gemini 2.0 Flash cho image generation
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            print("✅ AI Image Generator initialized with Gemini 2.0 Flash Exp")
            
        except Exception as e:
            print(f"❌ Error initializing AI Image Generator: {e}")
            raise
    
    def create_dollhouse_prompt(self, card_info, face_description=""):
        """Tạo prompt cho dollhouse miniature scene"""
        
        # Extract thông tin từ card
        name = card_info.get('name', 'Professional')
        company = card_info.get('company', 'Company')
        position = card_info.get('title', card_info.get('position', 'Employee'))
        
        # Tạo prompt chi tiết theo yêu cầu
        prompt = f"""Create a dollhouse miniature scene showing a professional work environment. 

SCENE REQUIREMENTS:
- Top-down wide angle view showing 2 wall corners and floor (not too high angle)
- Professional work environment specific to: {position} at {company}
- Full-body toy statue of a person in the middle, facing forward directly
- The person should have realistic facial features matching the reference photo
- Warm, cozy lighting with handmade textures
- Toy box perspective as if the model is placed on a table surface

ENVIRONMENT DETAILS:
- Create a {position} workspace with appropriate furniture and tools
- Include profession-specific items and equipment
- Miniature desk, chair, and work materials
- Small decorative elements that fit the profession

TEXT ELEMENTS:
- Small nameplate on desk showing: "{name}"
- Wall text/sign showing: "{company}"
- Professional and clean typography

STYLE:
- Dollhouse miniature aesthetic
- Handcrafted, detailed textures
- Warm lighting creating cozy atmosphere
- High quality, realistic miniature scene
- Colors should be warm and inviting

The scene should look like a carefully crafted dollhouse room that tells the story of this professional's work life."""

        return prompt
    
    def generate_dollhouse_images(self, face_image_path, card_info, output_folder, sessions_folder, num_images=2):
        """Generate dollhouse images từ face và card info với improved error handling"""
        try:
            print(f"🎨 Bắt đầu tạo {num_images} ảnh dollhouse...")

            # Kiểm tra API key
            if not os.getenv('GEMINI_API_KEY'):
                raise ValueError("❌ GEMINI_API_KEY không được thiết lập. Vui lòng cấu hình API key.")

            # Đọc face image
            if not os.path.exists(face_image_path):
                raise FileNotFoundError(f"❌ Face image not found: {face_image_path}")

            try:
                face_image = Image.open(face_image_path)
                print(f"📷 Loaded face image: {face_image.size}")
            except Exception as e:
                raise ValueError(f"❌ Không thể đọc face image: {e}")

            # Tạo prompt
            prompt = self.create_dollhouse_prompt(card_info)
            print(f"📝 Generated prompt: {len(prompt)} characters")

            generated_images = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            for i in range(num_images):
                try:
                    print(f"🎯 Generating image {i+1}/{num_images}...")

                    # Tạo ảnh với Gemini 2.0 Flash Exp
                    response = self.model.generate_content([
                        "Generate an image based on this prompt: " + prompt,
                        "Use this reference face photo for the miniature person:",
                        face_image
                    ])

                    # Kiểm tra response
                    if not response:
                        print(f"⚠️ Empty response for image {i+1}")
                        continue

                    # Kiểm tra có text response (có thể là lỗi)
                    if hasattr(response, 'text') and response.text:
                        print(f"⚠️ Text response instead of image: {response.text[:100]}...")
                        continue

                    # Kiểm tra có parts với image data
                    if hasattr(response, 'parts') and response.parts:
                        image_saved = False
                        for part in response.parts:
                            if hasattr(part, 'inline_data') and part.inline_data:
                                try:
                                    # Lưu ảnh vào outputs folder
                                    output_filename = f"dollhouse_{timestamp}_{i+1}.png"
                                    output_path = os.path.join(output_folder, output_filename)

                                    # Lưu binary data
                                    with open(output_path, 'wb') as f:
                                        f.write(part.inline_data.data)

                                    # Copy vào sessions folder
                                    sessions_path = os.path.join(sessions_folder, output_filename)
                                    with open(sessions_path, 'wb') as f:
                                        f.write(part.inline_data.data)

                                    generated_images.append(output_filename)
                                    print(f"✅ Saved image {i+1}: {output_filename}")
                                    image_saved = True
                                    break
                                except Exception as save_error:
                                    print(f"❌ Error saving image {i+1}: {save_error}")

                        if not image_saved:
                            print(f"⚠️ No valid image data in response for attempt {i+1}")
                    else:
                        print(f"⚠️ No parts in response for attempt {i+1}")

                    # Delay giữa các requests
                    if i < num_images - 1:
                        time.sleep(3)

                except Exception as e:
                    print(f"❌ Error generating image {i+1}: {e}")
                    # Thử tiếp với image tiếp theo
                    continue

            if generated_images:
                print(f"🎉 Successfully generated {len(generated_images)} images")
                return generated_images
            else:
                print("❌ No images were generated")
                return []

        except Exception as e:
            print(f"❌ Error in generate_dollhouse_images: {e}")
            return []
    
    def create_business_card_overlay(self, card_info):
        """Tạo overlay thông tin business card"""
        overlay_info = {
            'name': card_info.get('name', ''),
            'company': card_info.get('company', ''),
            'position': card_info.get('title', card_info.get('position', '')),
            'phone': card_info.get('phone', ''),
            'email': card_info.get('email', '')
        }
        
        # Chỉ giữ thông tin không rỗng
        filtered_info = {k: v for k, v in overlay_info.items() if v}
        
        return filtered_info
    
    def save_generation_metadata(self, card_info, generated_images, sessions_folder):
        """Lưu metadata của generation session"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            metadata = {
                'timestamp': timestamp,
                'card_info': card_info,
                'generated_images': generated_images,
                'generation_time': datetime.now().isoformat()
            }
            
            metadata_path = os.path.join(sessions_folder, f"metadata_{timestamp}.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Saved metadata: {metadata_path}")
            
        except Exception as e:
            print(f"⚠️ Error saving metadata: {e}")

# Test function
if __name__ == "__main__":
    # Test AI Image Generator
    generator = AIImageGenerator()
    
    # Test card info
    test_card_info = {
        'name': 'John Doe',
        'company': 'Tech Corp',
        'position': 'Software Engineer',
        'phone': '******-0123',
        'email': '<EMAIL>'
    }
    
    # Test face image path
    test_face_path = "test_face.jpg"
    
    if os.path.exists(test_face_path):
        result = generator.generate_dollhouse_images(
            face_image_path=test_face_path,
            card_info=test_card_info,
            output_folder="test_outputs",
            sessions_folder="test_sessions"
        )
        print("Test result:", result)
    else:
        print("Test face image not found")
