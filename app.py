from flask import Flask, render_template, request, jsonify, send_file, Response
import cv2
import numpy as np
from PIL import Image
import io
import base64
import os
import json
import threading
import time
from datetime import datetime
import google.generativeai as genai
from ocr_processor import OCRProcessor
from ai_generator import AIImageGenerator

app = Flask(__name__)

# Cấu hình thư mục
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Khởi tạo các processor
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

# Camera management class
class CameraManager:
    def __init__(self):
        self.card_camera = None  # Logitech C270 cho card visit
        self.face_camera = None  # Laptop camera cho face
        self.card_camera_index = 1  # Thường là USB camera
        self.face_camera_index = 0  # Thường là laptop camera
        self.focus_value = 100  # Giá trị focus mặc định cho Logitech C270
        self.is_streaming = False

    def initialize_cameras(self):
        """Khởi tạo 2 camera"""
        try:
            # Khởi tạo camera cho card visit (Logitech C270)
            self.card_camera = cv2.VideoCapture(self.card_camera_index)
            if self.card_camera.isOpened():
                # Cấu hình camera cho card visit
                self.card_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                self.card_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                # Tắt autofocus và set manual focus cho text clarity
                self.card_camera.set(cv2.CAP_PROP_AUTOFOCUS, 0)
                self.card_camera.set(cv2.CAP_PROP_FOCUS, self.focus_value)
                print(f"Card camera initialized successfully on index {self.card_camera_index}")

            # Khởi tạo camera cho face (laptop camera)
            self.face_camera = cv2.VideoCapture(self.face_camera_index)
            if self.face_camera.isOpened():
                # Cấu hình camera cho face
                self.face_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.face_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                print(f"Face camera initialized successfully on index {self.face_camera_index}")

            return True
        except Exception as e:
            print(f"Error initializing cameras: {e}")
            return False

    def adjust_focus(self, focus_value):
        """Điều chỉnh focus cho camera card visit"""
        if self.card_camera and self.card_camera.isOpened():
            self.focus_value = max(0, min(255, focus_value))
            self.card_camera.set(cv2.CAP_PROP_FOCUS, self.focus_value)
            return self.focus_value
        return None

    def capture_card_image(self):
        """Chụp ảnh từ camera card visit"""
        if self.card_camera and self.card_camera.isOpened():
            ret, frame = self.card_camera.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"card_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def capture_face_image(self):
        """Chụp ảnh từ camera face"""
        if self.face_camera and self.face_camera.isOpened():
            ret, frame = self.face_camera.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"face_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def get_card_frame(self):
        """Lấy frame từ camera card visit"""
        if self.card_camera and self.card_camera.isOpened():
            ret, frame = self.card_camera.read()
            if ret:
                return frame
        return None

    def get_face_frame(self):
        """Lấy frame từ camera face"""
        if self.face_camera and self.face_camera.isOpened():
            ret, frame = self.face_camera.read()
            if ret:
                return frame
        return None

    def release_cameras(self):
        """Giải phóng camera resources"""
        if self.card_camera:
            self.card_camera.release()
        if self.face_camera:
            self.face_camera.release()

# Khởi tạo camera manager
camera_manager = CameraManager()

@app.route('/')
def index():
    return render_template('camera_streaming.html')

# Camera streaming routes
@app.route('/initialize_cameras')
def initialize_cameras():
    """Khởi tạo cameras"""
    success = camera_manager.initialize_cameras()
    return jsonify({'success': success})

def generate_frames(camera_type):
    """Generator function cho video streaming"""
    while True:
        if camera_type == 'card':
            frame = camera_manager.get_card_frame()
        elif camera_type == 'face':
            frame = camera_manager.get_face_frame()
        else:
            break

        if frame is not None:
            # Encode frame thành JPEG
            ret, buffer = cv2.imencode('.jpg', frame)
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
        else:
            time.sleep(0.1)  # Tránh CPU overload khi không có frame

@app.route('/video_feed_card')
def video_feed_card():
    """Video streaming route cho camera card visit"""
    return Response(generate_frames('card'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_face')
def video_feed_face():
    """Video streaming route cho camera face"""
    return Response(generate_frames('face'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Điều chỉnh focus cho camera card visit"""
    data = request.get_json()
    focus_value = data.get('focus_value', 100)
    result = camera_manager.adjust_focus(focus_value)
    return jsonify({'success': result is not None, 'focus_value': result})

@app.route('/capture_card', methods=['POST'])
def capture_card():
    """Chụp ảnh từ camera card visit"""
    filepath = camera_manager.capture_card_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture card image'})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Chụp ảnh từ camera face"""
    filepath = camera_manager.capture_face_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture face image'})

@app.route('/process_and_generate', methods=['POST'])
def process_and_generate():
    """Xử lý OCR và tạo ảnh AI từ 2 ảnh đã chụp"""
    try:
        data = request.get_json()
        card_filepath = data.get('card_filepath')
        face_filepath = data.get('face_filepath')

        if not card_filepath or not face_filepath:
            return jsonify({'success': False, 'error': 'Missing image filepaths'})

        # Bước 1: OCR business card
        print("Processing OCR...")
        card_info = ocr_processor.extract_card_info(card_filepath)
        if not card_info:
            return jsonify({'success': False, 'error': 'Failed to extract card information'})

        # Bước 2: Tạo ảnh AI
        print("Generating AI images...")
        result = ai_generator.generate_dollhouse_images(
            face_image_path=face_filepath,
            card_info=card_info
        )

        if result['success']:
            # Tạo session để lưu kết quả
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_folder = os.path.join(SESSIONS_FOLDER, timestamp)
            os.makedirs(session_folder, exist_ok=True)

            # Lưu thông tin session
            session_data = {
                'timestamp': timestamp,
                'card_info': card_info,
                'card_image': card_filepath,
                'face_image': face_filepath,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            }

            session_file = os.path.join(session_folder, 'session_data.json')
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            return jsonify({
                'success': True,
                'session_id': timestamp,
                'card_info': card_info,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            })
        else:
            return jsonify({'success': False, 'error': result.get('error', 'Failed to generate images')})

    except Exception as e:
        print(f"Error in process_and_generate: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_session/<session_id>')
def get_session(session_id):
    """Lấy thông tin session"""
    try:
        session_file = os.path.join(SESSIONS_FOLDER, session_id, 'session_data.json')
        if os.path.exists(session_file):
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            return jsonify({'success': True, 'data': session_data})
        else:
            return jsonify({'success': False, 'error': 'Session not found'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Serve uploaded files để preview
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route('/outputs/<filename>')
def output_file(filename):
    return send_file(os.path.join(OUTPUT_FOLDER, filename))

@app.route('/download/<path:filename>')
def download_file(filename):
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Cleanup function
@app.teardown_appcontext
def cleanup_cameras(error):
    """Giải phóng camera resources khi app shutdown"""
    camera_manager.release_cameras()

if __name__ == '__main__':
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    finally:
        camera_manager.release_cameras()
