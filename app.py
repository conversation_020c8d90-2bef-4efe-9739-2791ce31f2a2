import cv2
from flask import Flask, render_template, Response, request, jsonify
import threading
import time
import os
from datetime import datetime

# Import các modules cho OCR và AI generation
from ocr_processor import OCRProcessor
from ai_image_generator import AIImageGenerator

app = Flask(__name__)

# Đảm bảo thư mục lưu ảnh tồn tại (theo project_directory structure)
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Global variables for camera management (theo project_directory approach)
camera_0 = None  # Camera 0: Logitech C270 cho business card (bên trái)
camera_1 = None  # Camera 1: Laptop camera cho face (bên phải)
output_frame_0 = None  # Frame hiện tại từ camera 0
output_frame_1 = None  # Frame hiện tại từ camera 1
lock_0 = threading.Lock()  # Lock cho camera 0
lock_1 = threading.Lock()  # Lock cho camera 1

# Giá trị focus ban đầu cho Camera 0 (Logitech C270)
initial_focus_value = 0  # Theo project_directory

# Initialize processors
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

def initialize_camera(camera_index, focus_value=None):
    """Khởi tạo camera theo project_directory approach"""
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_index}")
        return None

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    if focus_value is not None:
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
        cap.set(cv2.CAP_PROP_FOCUS, focus_value)
        print(f"Camera {camera_index} - Focus set to {focus_value}")

    return cap

def generate_frames(camera_id):
    """Tạo các khung hình từ camera để stream (theo project_directory approach)"""
    global camera_0, camera_1, output_frame_0, output_frame_1

    while True:
        cap = camera_0 if camera_id == 0 else camera_1
        lock = lock_0 if camera_id == 0 else lock_1

        if cap is None or not cap.isOpened():
            print(f"Camera {camera_id} chưa sẵn sàng.")
            time.sleep(2)
            continue

        ret, frame = cap.read()
        if not ret:
            print(f"Lỗi đọc camera {camera_id}. Đang khởi động lại...")
            cap.release()
            if camera_id == 0:
                with lock_0:
                    camera_0 = initialize_camera(0, initial_focus_value)
            else:
                with lock_1:
                    camera_1 = initialize_camera(1)
            time.sleep(1)
            continue

        with lock:
            if camera_id == 0:
                output_frame_0 = frame.copy()
            else:
                output_frame_1 = frame.copy()

        ret, buffer = cv2.imencode('.jpg', frame)
        frame = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

def setup_cameras():
    """Khởi tạo camera theo project_directory approach"""
    global camera_0, camera_1
    print("Đang khởi tạo camera...")
    camera_0 = initialize_camera(0, initial_focus_value)  # Logitech (bên trái)
    camera_1 = initialize_camera(1)  # Laptop (bên phải)

    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    """Video feed endpoint theo project_directory approach"""
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

# Routes cho compatibility với giao diện hiện tại
@app.route('/video_feed_card')
def video_feed_card():
    """Video feed cho business card camera (Camera 0 - Logitech bên trái)"""
    return Response(generate_frames(0),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_face')
def video_feed_face():
    """Video feed cho face camera (Camera 1 - Laptop bên phải)"""
    return Response(generate_frames(1),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/camera_status')
def camera_status():
    """Check camera status theo project_directory approach"""
    global camera_0, camera_1
    cap0_status = camera_0 is not None and camera_0.isOpened()
    cap1_status = camera_1 is not None and camera_1.isOpened()

    return jsonify({
        'mode': 'dual' if cap1_status else 'single',
        'camera0': {
            'available': cap0_status,
            'index': 0,
            'used_for': 'business_card'  # Camera 0 - Logitech bên trái
        },
        'camera1': {
            'available': cap1_status,
            'index': 1 if cap1_status else None,
            'used_for': 'face' if cap1_status else None  # Camera 1 - Laptop bên phải
        },
        'dual_mode': cap1_status
    })

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Điều chỉnh focus cho camera 0 (Logitech) theo project_directory approach"""
    global camera_0, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_0:
        if camera_0 and camera_0.isOpened():
            camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Focus mới cho Logitech: {new_focus_value}")
            return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

@app.route('/capture', methods=['POST'])
def capture_image():
    """Chụp ảnh từ cả 2 camera theo project_directory approach"""
    global output_frame_0, output_frame_1

    timestamp = int(time.time())
    image_paths = []

    with lock_0:
        if output_frame_0 is not None:
            filename_0 = os.path.join(UPLOAD_FOLDER, f'card_{timestamp}.jpg')
            cv2.imwrite(filename_0, output_frame_0)
            image_paths.append(filename_0)
            print(f"Lưu ảnh card: {filename_0}")

    with lock_1:
        if output_frame_1 is not None:
            filename_1 = os.path.join(UPLOAD_FOLDER, f'face_{timestamp}.jpg')
            cv2.imwrite(filename_1, output_frame_1)
            image_paths.append(filename_1)
            print(f"Lưu ảnh face: {filename_1}")

    if image_paths:
        return jsonify({'status': 'success', 'message': 'Đã chụp ảnh.', 'image_paths': image_paths})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500

@app.route('/capture_card', methods=['POST'])
def capture_card():
    """Chụp ảnh business card từ camera 0 (Logitech bên trái)"""
    global output_frame_0

    with lock_0:
        if output_frame_0 is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'card_{timestamp}.jpg'
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            cv2.imwrite(filepath, output_frame_0)
            print(f"Lưu business card: {filepath}")
            return jsonify({'success': True, 'filepath': filepath})
        else:
            return jsonify({'success': False, 'error': 'Không có frame từ camera business card'})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Chụp ảnh face từ camera 1 (Laptop bên phải)"""
    global output_frame_1

    with lock_1:
        if output_frame_1 is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'face_{timestamp}.jpg'
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            cv2.imwrite(filepath, output_frame_1)
            print(f"Lưu face: {filepath}")
            return jsonify({'success': True, 'filepath': filepath})
        else:
            return jsonify({'success': False, 'error': 'Không có frame từ camera face'})

@app.route('/process_and_generate', methods=['POST'])
def process_and_generate():
    """Xử lý OCR business card và tạo ảnh AI dollhouse với detailed error handling"""
    try:
        data = request.get_json()
        card_filepath = data.get('card_filepath')
        face_filepath = data.get('face_filepath')

        print(f"🚀 Bắt đầu xử lý: card={card_filepath}, face={face_filepath}")

        if not card_filepath or not face_filepath:
            return jsonify({'success': False, 'error': 'Thiếu đường dẫn ảnh'})

        # Kiểm tra file tồn tại
        if not os.path.exists(card_filepath):
            return jsonify({'success': False, 'error': f'Không tìm thấy file business card: {card_filepath}'})

        if not os.path.exists(face_filepath):
            return jsonify({'success': False, 'error': f'Không tìm thấy file face: {face_filepath}'})

        # Step 1: OCR business card
        print(f"🔍 Đang xử lý OCR cho: {card_filepath}")
        try:
            card_info = ocr_processor.extract_card_info(card_filepath)
            print(f"📋 OCR result: {card_info}")
        except Exception as ocr_error:
            print(f"❌ OCR Error: {ocr_error}")
            return jsonify({'success': False, 'error': f'Lỗi OCR: {str(ocr_error)}'})

        if not card_info or not any(card_info.values()):
            return jsonify({'success': False, 'error': 'Không thể trích xuất thông tin từ business card. Vui lòng thử lại với ảnh rõ nét hơn.'})

        # Step 2: Kiểm tra GEMINI_API_KEY
        if not os.getenv('GEMINI_API_KEY'):
            return jsonify({'success': False, 'error': 'GEMINI_API_KEY chưa được cấu hình. Vui lòng thiết lập API key.'})

        # Step 3: Generate AI dollhouse images
        print(f"🎨 Đang tạo ảnh AI dollhouse...")
        try:
            generated_images = ai_generator.generate_dollhouse_images(
                face_image_path=face_filepath,
                card_info=card_info,
                output_folder=OUTPUT_FOLDER,
                sessions_folder=SESSIONS_FOLDER
            )
            print(f"🖼️ Generated images: {generated_images}")
        except Exception as ai_error:
            print(f"❌ AI Generation Error: {ai_error}")
            return jsonify({'success': False, 'error': f'Lỗi tạo ảnh AI: {str(ai_error)}'})

        if not generated_images:
            return jsonify({'success': False, 'error': 'Không thể tạo ảnh AI. Có thể do API key không hợp lệ hoặc quota đã hết.'})

        return jsonify({
            'success': True,
            'card_info': card_info,
            'generated_images': generated_images,
            'message': f'Xử lý thành công! Đã tạo {len(generated_images)} ảnh AI.'
        })

    except Exception as e:
        print(f"❌ Lỗi trong process_and_generate: {e}")
        return jsonify({'success': False, 'error': f'Lỗi hệ thống: {str(e)}'})

@app.route('/download/<filename>')
def download_file(filename):
    """Download generated images"""
    from flask import send_from_directory
    return send_from_directory(OUTPUT_FOLDER, filename)

if __name__ == '__main__':
    # Gọi setup_cameras trong app context
    with app.app_context():
        setup_cameras()
    app.run(host='0.0.0.0', port=5000, debug=False)
