from flask import Flask, render_template, request, jsonify, send_file
import cv2
import numpy as np
from PIL import Image
import io
import base64
import os
import json
from datetime import datetime
import google.generativeai as genai
from ocr_processor import OCRProcessor
from ai_generator import AIImageGenerator

app = Flask(__name__)

# Cấu hình thư mục
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Khởi tạo các processor
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

@app.route('/')
def index():
    return render_template('index.html')

# STEP 1: Upload và xử lý name card (thay thế camera 1)
@app.route('/upload_card', methods=['POST'])
def upload_card():
    """
    Upload name card từ laptop và trích xuất thông tin bằng Gemini
    Gemini tốt hơn Tesseract cho OCR vì:
    - Hiểu context và layout tốt hơn
    - Xử lý được nhiều font và ngôn ngữ
    - Có thể hiểu cấu trúc business card
    """
    try:
        if 'card_image' not in request.files:
            return jsonify({'success': False, 'error': 'No card image uploaded'})
        
        file = request.files['card_image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})
        
        # Save card image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        card_filename = f"card_{timestamp}.jpg"
        card_path = os.path.join(UPLOAD_FOLDER, card_filename)
        file.save(card_path)
        
        # Extract text from card using Gemini (better than Tesseract)
        extracted_info = ocr_processor.extract_card_info(card_path)
        
        return jsonify({
            'success': True,
            'card_path': card_path,
            'card_url': f'/uploads/{card_filename}',
            'extracted_info': extracted_info,
            'message': 'Card uploaded and processed successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# STEP 2: Capture face từ laptop camera (thay thế camera 2)
@app.route('/capture_face', methods=['POST'])
def capture_face():
    """
    Chụp ảnh khuôn mặt từ laptop camera
    Chỉ thực hiện sau khi đã xác nhận thông tin card đúng
    """
    try:
        data = request.get_json()
        image_data = data['image'].split(',')[1]
        
        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Save face image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        face_filename = f"face_{timestamp}.jpg"
        face_path = os.path.join(UPLOAD_FOLDER, face_filename)
        image.save(face_path)
        
        return jsonify({
            'success': True,
            'face_path': face_path,
            'message': 'Face captured successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# STEP 3: Generate AI dollhouse image với text tích hợp như model1.png
@app.route('/generate_ai_image', methods=['POST'])
def generate_ai_image():
    """
    Tạo ảnh AI dollhouse với:
    - Khuôn mặt từ camera laptop
    - Text từ name card được tích hợp vào ảnh (như model1.png)
    - Style dollhouse miniature với text hiển thị trên tường/nameplate
    """
    try:
        data = request.get_json()
        face_path = data.get('face_path')
        extracted_info = data.get('extracted_info', {})
        
        if not face_path or not os.path.exists(face_path):
            return jsonify({'success': False, 'error': 'Face image not found'})
        
        # Generate AI images với text được tích hợp vào ảnh như model1.png
        result = ai_generator.generate_dollhouse_images(face_path, extracted_info)
        
        if result['success']:
            return jsonify({
                'success': True,
                'images': result['images'],
                'message': 'AI images generated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Serve uploaded files để preview
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route('/download/<path:filename>')
def download_file(filename):
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
