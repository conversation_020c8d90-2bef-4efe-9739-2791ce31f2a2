from flask import Flask, render_template, request, jsonify, send_file, Response
import cv2
import numpy as np
from PIL import Image
import io
import base64
import os
import json
import threading
import time
from datetime import datetime
import google.generativeai as genai
from ocr_processor import OCRProcessor
from ai_generator import AIImageGenerator

app = Flask(__name__)

# C<PERSON>u hình thư mục
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Khởi tạo các processor
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

# Camera management class - Simple and reliable approach
class CameraManager:
    def __init__(self):
        self.cap1 = None  # Camera 1 (Business card)
        self.cap2 = None  # Camera 2 (Face) - có thể None nếu chỉ có 1 camera
        self.camera1_index = 0  # Default camera (luôn sử dụng)
        self.camera2_index = 1  # External camera (optional)
        self.focus_value = 100
        self.dual_mode = False  # Sẽ được set True nếu có 2 camera hoạt động

    def initialize_cameras(self):
        """Initialize cameras using simple and reliable approach"""
        print("🔍 Initializing cameras...")

        # Initialize first camera (Business card) with DirectShow backend
        print(f"Testing camera {self.camera1_index} for business card...")
        self.cap1 = cv2.VideoCapture(self.camera1_index, cv2.CAP_DSHOW)

        if not self.cap1.isOpened():
            print(f"❌ Error: Could not open camera {self.camera1_index}")
            # Try without DirectShow
            print(f"Trying camera {self.camera1_index} without DirectShow...")
            self.cap1 = cv2.VideoCapture(self.camera1_index)
            if not self.cap1.isOpened():
                print(f"❌ Error: Could not open camera {self.camera1_index} at all")
                return False

        # Test reading frame from camera 1
        ret1, _ = self.cap1.read()
        if not ret1:
            print(f"❌ Error: Could not read frame from camera {self.camera1_index}")
            self.cap1.release()
            return False

        print(f"✅ Camera {self.camera1_index} (Business card) initialized successfully")

        # Configure camera 1
        self.cap1.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.cap1.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.cap1.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # Try to enable manual focus for camera 1
        try:
            self.cap1.set(cv2.CAP_PROP_AUTOFOCUS, 0)
            self.cap1.set(cv2.CAP_PROP_FOCUS, self.focus_value)
            print(f"✅ Manual focus enabled for camera {self.camera1_index}")
        except:
            print(f"⚠️ Manual focus not supported for camera {self.camera1_index}")

        # Try to initialize second camera (Face) - với timeout ngắn
        print(f"Testing camera {self.camera2_index} for face (with timeout)...")

        try:
            # Thử với timeout ngắn để tránh treo
            import threading
            import time

            def try_camera2():
                self.cap2 = cv2.VideoCapture(self.camera2_index, cv2.CAP_DSHOW)
                if self.cap2.isOpened():
                    ret, _ = self.cap2.read()
                    if not ret:
                        self.cap2.release()
                        self.cap2 = None

            # Chạy với timeout 3 giây
            thread = threading.Thread(target=try_camera2)
            thread.daemon = True
            thread.start()
            thread.join(timeout=3.0)

            if thread.is_alive():
                print(f"⚠️ Camera {self.camera2_index} initialization timed out")
                self.cap2 = None
            elif self.cap2 and self.cap2.isOpened():
                print(f"✅ Camera {self.camera2_index} (Face) initialized successfully")
                # Configure camera 2
                self.cap2.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.cap2.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.cap2.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.dual_mode = True
            else:
                print(f"⚠️ Camera {self.camera2_index} not available")
                self.cap2 = None

        except Exception as e:
            print(f"⚠️ Error testing camera {self.camera2_index}: {e}")
            self.cap2 = None

        if not self.dual_mode:
            print(f"📷 Using single camera mode (camera {self.camera1_index} for both)")
        else:
            print(f"📷 Dual camera mode enabled - Card: {self.camera1_index}, Face: {self.camera2_index}")

        return True

    def adjust_focus(self, focus_value):
        """Adjust focus for camera 1 (business card)"""
        if self.cap1 and self.cap1.isOpened():
            self.focus_value = max(0, min(255, focus_value))
            try:
                self.cap1.set(cv2.CAP_PROP_FOCUS, self.focus_value)
                return self.focus_value
            except:
                return None
        return None

    def capture_card_image(self):
        """Capture business card image from camera 1"""
        if self.cap1 and self.cap1.isOpened():
            ret, frame = self.cap1.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"card_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def capture_face_image(self):
        """Capture face image from camera 2 (or camera 1 if single mode)"""
        camera = self.cap2 if self.dual_mode and self.cap2 else self.cap1
        if camera and camera.isOpened():
            ret, frame = camera.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"face_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def get_card_frame(self):
        """Get frame from camera 1 (business card)"""
        try:
            if self.cap1 and self.cap1.isOpened():
                ret, frame = self.cap1.read()
                if ret and frame is not None:
                    return frame
        except Exception as e:
            print(f"❌ Error reading card frame: {e}")
        return None

    def get_face_frame(self):
        """Get frame from camera 2 (face) or camera 1 if single mode"""
        try:
            camera = self.cap2 if self.dual_mode and self.cap2 else self.cap1
            if camera and camera.isOpened():
                ret, frame = camera.read()
                if ret and frame is not None:
                    return frame
        except Exception as e:
            print(f"❌ Error reading face frame: {e}")
        return None

    def release_cameras(self):
        """Release camera resources"""
        if self.cap1:
            self.cap1.release()
            self.cap1 = None
        if self.cap2:
            self.cap2.release()
            self.cap2 = None

# Khởi tạo camera manager
camera_manager = CameraManager()

# Khởi tạo cameras khi server start
print("🚀 Initializing cameras on server startup...")
camera_manager.initialize_cameras()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/new')
def new_interface():
    return render_template('index.html')

# Camera streaming routes
@app.route('/initialize_cameras')
def initialize_cameras():
    """Khởi tạo cameras"""
    success = camera_manager.initialize_cameras()
    return jsonify({'success': success})

@app.route('/camera_status')
def camera_status():
    """Check camera status"""
    cap1_status = camera_manager.cap1 is not None and camera_manager.cap1.isOpened()
    cap2_status = camera_manager.cap2 is not None and camera_manager.cap2.isOpened()

    return jsonify({
        'mode': 'dual' if camera_manager.dual_mode else 'single',
        'camera1': {
            'available': cap1_status,
            'index': camera_manager.camera1_index,
            'used_for': 'business_card'
        },
        'camera2': {
            'available': cap2_status,
            'index': camera_manager.camera2_index if cap2_status else None,
            'used_for': 'face' if camera_manager.dual_mode else None
        },
        'dual_mode': camera_manager.dual_mode
    })

def generate_frames(camera_type):
    """Generator function cho video streaming - Improved version"""
    frame_count = 0

    while True:
        try:
            frame = None

            # Lấy frame dựa trên camera type
            if camera_type == 'card':
                frame = camera_manager.get_card_frame()
            elif camera_type == 'face':
                frame = camera_manager.get_face_frame()
            else:
                break

            if frame is not None:
                # Resize frame để tối ưu bandwidth
                if camera_type == 'card':
                    frame = cv2.resize(frame, (640, 480))
                else:
                    frame = cv2.resize(frame, (480, 360))

                # Encode frame thành JPEG với quality tối ưu
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 85]
                ret, buffer = cv2.imencode('.jpg', frame, encode_param)

                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                else:
                    # Fallback frame
                    fallback_frame = create_black_frame(f"Encoding failed for {camera_type}")
                    ret, buffer = cv2.imencode('.jpg', fallback_frame)
                    if ret:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            else:
                # Tạo frame thông báo khi không có camera
                status_text = f"Camera {camera_type} not available"
                if camera_manager.camera_mode == 'single':
                    status_text = f"Single camera mode - {camera_type} view"

                status_frame = create_black_frame(status_text)
                ret, buffer = cv2.imencode('.jpg', status_frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            # Control frame rate
            time.sleep(0.1)  # 10 FPS để giảm tải
            frame_count += 1

        except Exception as e:
            print(f"❌ Error in generate_frames for {camera_type}: {e}")
            # Tạo frame lỗi
            error_frame = create_black_frame(f"Error: {camera_type}")
            try:
                ret, buffer = cv2.imencode('.jpg', error_frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            except:
                pass
            time.sleep(1)

def create_black_frame(text="No Camera"):
    """Tạo frame đen với text"""
    import numpy as np
    frame = np.zeros((480, 640, 3), dtype=np.uint8)

    # Thêm background
    cv2.rectangle(frame, (0, 0), (640, 480), (30, 30, 30), -1)

    # Thêm text với font size phù hợp
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8
    color = (255, 255, 255)
    thickness = 2

    # Tính toán vị trí text để center
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (640 - text_size[0]) // 2
    text_y = (480 + text_size[1]) // 2

    cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness)

    # Thêm timestamp
    import datetime
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    cv2.putText(frame, timestamp, (10, 30), font, 0.5, (128, 128, 128), 1)

    return frame

@app.route('/video_feed_card')
def video_feed_card():
    """Video streaming route cho camera card visit"""
    return Response(generate_frames('card'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_face')
def video_feed_face():
    """Video streaming route cho camera face"""
    return Response(generate_frames('face'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Điều chỉnh focus cho camera card visit"""
    data = request.get_json()
    focus_value = data.get('focus_value', 100)
    result = camera_manager.adjust_focus(focus_value)
    return jsonify({'success': result is not None, 'focus_value': result})

@app.route('/capture_card', methods=['POST'])
def capture_card():
    """Chụp ảnh từ camera card visit"""
    filepath = camera_manager.capture_card_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture card image'})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Chụp ảnh từ camera face"""
    filepath = camera_manager.capture_face_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture face image'})

@app.route('/process_and_generate', methods=['POST'])
def process_and_generate():
    """Xử lý OCR và tạo ảnh AI từ 2 ảnh đã chụp"""
    try:
        data = request.get_json()
        card_filepath = data.get('card_filepath')
        face_filepath = data.get('face_filepath')

        if not card_filepath or not face_filepath:
            return jsonify({'success': False, 'error': 'Missing image filepaths'})

        # Bước 1: OCR business card
        print("Processing OCR...")
        card_info = ocr_processor.extract_card_info(card_filepath)
        if not card_info:
            return jsonify({'success': False, 'error': 'Failed to extract card information'})

        # Bước 2: Tạo ảnh AI
        print("Generating AI images...")
        result = ai_generator.generate_dollhouse_images(
            face_image_path=face_filepath,
            card_info=card_info
        )

        if result['success']:
            # Tạo session để lưu kết quả
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_folder = os.path.join(SESSIONS_FOLDER, timestamp)
            os.makedirs(session_folder, exist_ok=True)

            # Lưu thông tin session
            session_data = {
                'timestamp': timestamp,
                'card_info': card_info,
                'card_image': card_filepath,
                'face_image': face_filepath,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            }

            session_file = os.path.join(session_folder, 'session_data.json')
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            return jsonify({
                'success': True,
                'session_id': timestamp,
                'card_info': card_info,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            })
        else:
            return jsonify({'success': False, 'error': result.get('error', 'Failed to generate images')})

    except Exception as e:
        print(f"Error in process_and_generate: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_session/<session_id>')
def get_session(session_id):
    """Lấy thông tin session"""
    try:
        session_file = os.path.join(SESSIONS_FOLDER, session_id, 'session_data.json')
        if os.path.exists(session_file):
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            return jsonify({'success': True, 'data': session_data})
        else:
            return jsonify({'success': False, 'error': 'Session not found'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Serve uploaded files để preview
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route('/outputs/<filename>')
def output_file(filename):
    return send_file(os.path.join(OUTPUT_FOLDER, filename))

@app.route('/download/<path:filename>')
def download_file(filename):
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Cleanup function
@app.teardown_appcontext
def cleanup_cameras(error=None):
    """Giải phóng camera resources khi app shutdown"""
    camera_manager.release_cameras()

if __name__ == '__main__':
    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
    finally:
        camera_manager.release_cameras()
