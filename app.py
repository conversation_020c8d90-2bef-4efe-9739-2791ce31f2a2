import cv2
from flask import Flask, render_template, Response, request, jsonify
import threading
import time
import os
import numpy as np
from datetime import datetime

# Import các modules cho OCR và AI generation
from ocr_processor import OCRProcessor
from ai_image_generator import AIImageGenerator

app = Flask(__name__)

# Đ<PERSON><PERSON> bảo thư mục lưu ảnh tồn tại
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Global variables for camera management (theo approach thành công từ project_directory)
camera_0 = None  # Camera cho business card (Logitech C270)
camera_1 = None  # Camera cho face (Laptop camera)
output_frame_0 = None  # Frame hiện tại từ camera 0
output_frame_1 = None  # Frame hiện tại từ camera 1
lock_0 = threading.Lock()  # Lock cho camera 0
lock_1 = threading.Lock()  # Lock cho camera 1

# Giá trị focus ban đầu cho Camera 0 (Logitech C270)
initial_focus_value = 100

# Initialize processors
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

def initialize_camera(camera_index, focus_value=None):
    """Khởi tạo camera và thiết lập các thuộc tính (theo project_directory approach)"""
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)  # Sử dụng cv2.CAP_DSHOW cho Windows
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_index}")
        return None

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    if focus_value is not None:
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # Tắt autofocus
        cap.set(cv2.CAP_PROP_FOCUS, focus_value)  # Đặt giá trị focus
        print(f"Camera {camera_index} - Autofocus: {cap.get(cv2.CAP_PROP_AUTOFOCUS)}, Focus: {cap.get(cv2.CAP_PROP_FOCUS)}")

    return cap

def generate_frames(camera_id):
    """Tạo các khung hình từ camera để stream (theo project_directory approach)"""
    global camera_0, camera_1, output_frame_0, output_frame_1, lock_0, lock_1

    cap = None
    if camera_id == 0:
        cap = camera_0
        current_lock = lock_0
    elif camera_id == 1:
        cap = camera_1
        current_lock = lock_1

    while True:
        # Lấy cap từ biến global (đã được khởi tạo)
        if camera_id == 0:
            cap = camera_0
            current_lock = lock_0
        elif camera_id == 1:
            cap = camera_1
            current_lock = lock_1

        if cap is None or not cap.isOpened():
            print(f"Camera {camera_id} chưa sẵn sàng hoặc bị mất kết nối. Đang chờ...")
            time.sleep(2)
            continue

        ret, frame = cap.read()
        if not ret:
            print(f"Không thể đọc khung hình từ camera {camera_id}. Đang thử lại sau khi reset...")
            # Đóng và mở lại camera để reset
            cap.release()
            if camera_id == 0:
                with lock_0:
                    camera_0 = initialize_camera(0, initial_focus_value)
            elif camera_id == 1:
                with lock_1:
                    camera_1 = initialize_camera(1)
            time.sleep(1)
            continue

        with current_lock:
            if camera_id == 0:
                output_frame_0 = frame.copy()
            elif camera_id == 1:
                output_frame_1 = frame.copy()

        ret, buffer = cv2.imencode('.jpg', frame)
        frame = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

# Hàm khởi tạo camera (theo project_directory approach)
def setup_cameras():
    """Khởi tạo camera khi ứng dụng Flask bắt đầu."""
    global camera_0, camera_1
    print("🚀 Khởi tạo camera...")
    camera_0 = initialize_camera(0)  # Camera 0 cho Face (Laptop camera)
    camera_1 = initialize_camera(1, initial_focus_value)  # Camera 1 cho Business Card (Logitech C270 với manual focus)

    # Bắt đầu luồng đọc khung hình cho mỗi camera
    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    """Video feed endpoint theo project_directory approach"""
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_card')
def video_feed_card():
    """Video feed cho business card camera (Camera 1 - Logitech C270)"""
    return Response(generate_frames(1),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_face')
def video_feed_face():
    """Video feed cho face camera (Camera 0 - Laptop camera)"""
    return Response(generate_frames(0),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/camera_status')
def camera_status():
    """Check camera status"""
    global camera_0, camera_1
    cap0_status = camera_0 is not None and camera_0.isOpened()
    cap1_status = camera_1 is not None and camera_1.isOpened()
    
    return jsonify({
        'mode': 'dual' if cap1_status else 'single',
        'camera0': {
            'available': cap0_status,
            'index': 0,
            'used_for': 'face'  # Camera 0 cho face
        },
        'camera1': {
            'available': cap1_status,
            'index': 1 if cap1_status else None,
            'used_for': 'business_card' if cap1_status else None  # Camera 1 cho business card
        },
        'dual_mode': cap1_status
    })

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Điều chỉnh focus cho camera 1 (business card - Logitech C270)"""
    global camera_1, lock_1, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_1:
        if camera_1 and camera_1.isOpened():
            camera_1.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Đã điều chỉnh focus Camera 1 (Business Card) lên: {camera_1.get(cv2.CAP_PROP_FOCUS)}")
            return jsonify({'status': 'success', 'message': f'Focus Camera 1 đã được điều chỉnh thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera 1 (Business Card) không hoạt động hoặc không tìm thấy.'}), 500

@app.route('/capture', methods=['POST'])
def capture_image():
    """Chụp ảnh từ cả 2 camera (theo project_directory approach)"""
    global output_frame_0, output_frame_1, lock_0, lock_1

    timestamp = int(time.time())
    image_paths = []

    with lock_0:
        if output_frame_0 is not None:
            filename_0 = os.path.join(UPLOAD_FOLDER, f'card_{timestamp}.jpg')
            cv2.imwrite(filename_0, output_frame_0)
            image_paths.append(filename_0)
            print(f"Đã lưu ảnh từ Camera 0 (Business card): {filename_0}")
        else:
            print("Không có khung hình từ Camera 0 để chụp.")

    with lock_1:
        if output_frame_1 is not None:
            filename_1 = os.path.join(UPLOAD_FOLDER, f'face_{timestamp}.jpg')
            cv2.imwrite(filename_1, output_frame_1)
            image_paths.append(filename_1)
            print(f"Đã lưu ảnh từ Camera 1 (Face): {filename_1}")
        else:
            print("Không có khung hình từ Camera 1 để chụp.")

    if image_paths:
        return jsonify({'status': 'success', 'message': 'Ảnh đã được chụp và lưu.', 'image_paths': image_paths})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh từ cả hai camera.'}), 500

@app.route('/capture_card', methods=['POST'])
def capture_card():
    """Chụp ảnh business card từ camera 1 (Logitech C270)"""
    global output_frame_1, lock_1

    with lock_1:
        if output_frame_1 is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'card_{timestamp}.jpg'
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            cv2.imwrite(filepath, output_frame_1)
            print(f"Đã lưu ảnh business card từ Camera 1: {filepath}")
            return jsonify({'success': True, 'filepath': filepath})
        else:
            return jsonify({'success': False, 'error': 'Không có frame từ camera business card (Camera 1)'})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Chụp ảnh face từ camera 0 (Laptop camera)"""
    global output_frame_0, lock_0

    with lock_0:
        if output_frame_0 is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'face_{timestamp}.jpg'
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            cv2.imwrite(filepath, output_frame_0)
            print(f"Đã lưu ảnh face từ Camera 0: {filepath}")
            return jsonify({'success': True, 'filepath': filepath})
        else:
            return jsonify({'success': False, 'error': 'Không có frame từ camera face (Camera 0)'})

@app.route('/process_and_generate', methods=['POST'])
def process_and_generate():
    """Xử lý OCR business card và tạo ảnh AI dollhouse"""
    try:
        data = request.get_json()
        card_filepath = data.get('card_filepath')
        face_filepath = data.get('face_filepath')

        if not card_filepath or not face_filepath:
            return jsonify({'success': False, 'error': 'Thiếu đường dẫn ảnh'})

        # Step 1: OCR business card
        print(f"🔍 Đang xử lý OCR cho: {card_filepath}")
        card_info = ocr_processor.extract_card_info(card_filepath)

        if not card_info:
            return jsonify({'success': False, 'error': 'Không thể trích xuất thông tin từ business card'})

        # Step 2: Generate AI dollhouse images
        print(f"🎨 Đang tạo ảnh AI dollhouse...")
        generated_images = ai_generator.generate_dollhouse_images(
            face_image_path=face_filepath,
            card_info=card_info,
            output_folder=OUTPUT_FOLDER,
            sessions_folder=SESSIONS_FOLDER
        )

        if not generated_images:
            return jsonify({'success': False, 'error': 'Không thể tạo ảnh AI'})

        return jsonify({
            'success': True,
            'card_info': card_info,
            'generated_images': generated_images,
            'message': 'Xử lý thành công!'
        })

    except Exception as e:
        print(f"❌ Lỗi trong process_and_generate: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/download/<filename>')
def download_file(filename):
    """Download generated images"""
    from flask import send_from_directory
    return send_from_directory(OUTPUT_FOLDER, filename)

if __name__ == '__main__':
    # Gọi setup_cameras trong app context
    with app.app_context():
        setup_cameras()
    app.run(host='0.0.0.0', port=5000, debug=False)
