from flask import Flask, render_template, request, jsonify, send_file, Response
import cv2
import numpy as np
from PIL import Image
import io
import base64
import os
import json
import threading
import time
from datetime import datetime
import google.generativeai as genai
from ocr_processor import OCRProcessor
from ai_generator import AIImageGenerator

app = Flask(__name__)

# Cấu hình thư mục
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
SESSIONS_FOLDER = 'sessions'

for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, SESSIONS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

# Khởi tạo các processor
ocr_processor = OCRProcessor()
ai_generator = AIImageGenerator()

# Camera management class - Simplified version
class CameraManager:
    def __init__(self):
        self.primary_camera = None  # Camera chính (có thể là laptop hoặc USB)
        self.secondary_camera = None  # Camera phụ (nếu có)
        self.primary_index = 0  # Camera chính
        self.secondary_index = 1  # Camera phụ
        self.focus_value = 100  # Giá trị focus mặc định
        self.is_streaming = False
        self.camera_mode = 'single'  # 'single' hoặc 'dual'

    def initialize_cameras(self):
        """Khởi tạo camera - approach đơn giản hơn"""
        print("🔍 Scanning for available cameras...")

        # Tìm tất cả camera có sẵn với timeout
        available_cameras = []
        for i in range(3):  # Giảm xuống 3 để tránh scan quá lâu
            print(f"Testing camera index {i}...")
            try:
                # Thử khởi tạo camera với timeout ngắn
                cap = cv2.VideoCapture(i, cv2.CAP_DSHOW)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Giảm buffer để tăng tốc

                if cap.isOpened():
                    # Test đọc frame với timeout
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        available_cameras.append(i)
                        print(f"✅ Camera {i} is available")
                    else:
                        print(f"❌ Camera {i} opened but cannot read frames")
                else:
                    print(f"❌ Camera {i} cannot be opened")
                cap.release()
            except Exception as e:
                print(f"❌ Error testing camera {i}: {e}")
                try:
                    cap.release()
                except:
                    pass

        print(f"📷 Found {len(available_cameras)} available cameras: {available_cameras}")

        if len(available_cameras) == 0:
            print("❌ No cameras found!")
            return False
        elif len(available_cameras) == 1:
            # Chỉ có 1 camera - sử dụng cho cả 2 mục đích
            self.camera_mode = 'single'
            self.primary_index = available_cameras[0]
            print(f"📷 Single camera mode - using camera {self.primary_index} for both card and face")

            self.primary_camera = cv2.VideoCapture(self.primary_index, cv2.CAP_DSHOW)
            if self.primary_camera.isOpened():
                # Cấu hình camera
                self.primary_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                self.primary_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                self.primary_camera.set(cv2.CAP_PROP_FPS, 30)

                # Thử set manual focus
                try:
                    self.primary_camera.set(cv2.CAP_PROP_AUTOFOCUS, 0)
                    self.primary_camera.set(cv2.CAP_PROP_FOCUS, self.focus_value)
                    print(f"✅ Manual focus enabled")
                except:
                    print(f"⚠️ Manual focus not supported")

                print(f"✅ Primary camera initialized successfully")
                return True
        else:
            # Có nhiều camera - sử dụng dual mode
            self.camera_mode = 'dual'
            self.primary_index = available_cameras[0]
            self.secondary_index = available_cameras[1]
            print(f"📷 Dual camera mode - Primary: {self.primary_index}, Secondary: {self.secondary_index}")

            # Khởi tạo camera chính
            self.primary_camera = cv2.VideoCapture(self.primary_index, cv2.CAP_DSHOW)
            if self.primary_camera.isOpened():
                self.primary_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                self.primary_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                self.primary_camera.set(cv2.CAP_PROP_FPS, 30)

                try:
                    self.primary_camera.set(cv2.CAP_PROP_AUTOFOCUS, 0)
                    self.primary_camera.set(cv2.CAP_PROP_FOCUS, self.focus_value)
                    print(f"✅ Manual focus enabled for primary camera")
                except:
                    print(f"⚠️ Manual focus not supported for primary camera")

                print(f"✅ Primary camera initialized")

            # Khởi tạo camera phụ
            self.secondary_camera = cv2.VideoCapture(self.secondary_index, cv2.CAP_DSHOW)
            if self.secondary_camera.isOpened():
                self.secondary_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.secondary_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.secondary_camera.set(cv2.CAP_PROP_FPS, 30)
                print(f"✅ Secondary camera initialized")

            return True

        return False

    def adjust_focus(self, focus_value):
        """Điều chỉnh focus cho camera chính"""
        if self.primary_camera and self.primary_camera.isOpened():
            self.focus_value = max(0, min(255, focus_value))
            try:
                self.primary_camera.set(cv2.CAP_PROP_FOCUS, self.focus_value)
                return self.focus_value
            except:
                return None
        return None

    def capture_card_image(self):
        """Chụp ảnh business card"""
        camera = self.primary_camera  # Luôn dùng camera chính cho card
        if camera and camera.isOpened():
            ret, frame = camera.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"card_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def capture_face_image(self):
        """Chụp ảnh khuôn mặt"""
        # Nếu có 2 camera, dùng camera phụ cho face, nếu không dùng camera chính
        camera = self.secondary_camera if self.camera_mode == 'dual' and self.secondary_camera else self.primary_camera
        if camera and camera.isOpened():
            ret, frame = camera.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"face_{timestamp}.jpg"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                cv2.imwrite(filepath, frame)
                return filepath
        return None

    def get_card_frame(self):
        """Lấy frame cho card camera stream"""
        try:
            camera = self.primary_camera  # Luôn dùng camera chính cho card
            if camera and camera.isOpened():
                ret, frame = camera.read()
                if ret and frame is not None:
                    return frame
                else:
                    print(f"⚠️ Card camera read failed")
        except Exception as e:
            print(f"❌ Error reading card frame: {e}")
        return None

    def get_face_frame(self):
        """Lấy frame cho face camera stream"""
        try:
            # Nếu có 2 camera, dùng camera phụ cho face, nếu không dùng camera chính
            camera = self.secondary_camera if self.camera_mode == 'dual' and self.secondary_camera else self.primary_camera
            if camera and camera.isOpened():
                ret, frame = camera.read()
                if ret and frame is not None:
                    return frame
                else:
                    print(f"⚠️ Face camera read failed")
        except Exception as e:
            print(f"❌ Error reading face frame: {e}")
        return None

    def release_cameras(self):
        """Giải phóng camera resources"""
        if self.primary_camera:
            self.primary_camera.release()
            self.primary_camera = None
        if self.secondary_camera:
            self.secondary_camera.release()
            self.secondary_camera = None

# Khởi tạo camera manager
camera_manager = CameraManager()

# Khởi tạo cameras khi server start
print("🚀 Initializing cameras on server startup...")
camera_manager.initialize_cameras()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/new')
def new_interface():
    return render_template('index.html')

# Camera streaming routes
@app.route('/initialize_cameras')
def initialize_cameras():
    """Khởi tạo cameras"""
    success = camera_manager.initialize_cameras()
    return jsonify({'success': success})

@app.route('/camera_status')
def camera_status():
    """Kiểm tra trạng thái camera"""
    primary_status = camera_manager.primary_camera is not None and camera_manager.primary_camera.isOpened()
    secondary_status = camera_manager.secondary_camera is not None and camera_manager.secondary_camera.isOpened()

    return jsonify({
        'mode': camera_manager.camera_mode,
        'primary_camera': {
            'available': primary_status,
            'index': camera_manager.primary_index if primary_status else None,
            'used_for': 'card' if camera_manager.camera_mode == 'single' else 'card'
        },
        'secondary_camera': {
            'available': secondary_status,
            'index': camera_manager.secondary_index if secondary_status else None,
            'used_for': 'face' if camera_manager.camera_mode == 'dual' else None
        },
        'card_camera_source': 'primary',
        'face_camera_source': 'secondary' if camera_manager.camera_mode == 'dual' else 'primary'
    })

def generate_frames(camera_type):
    """Generator function cho video streaming - Improved version"""
    frame_count = 0

    while True:
        try:
            frame = None

            # Lấy frame dựa trên camera type
            if camera_type == 'card':
                frame = camera_manager.get_card_frame()
            elif camera_type == 'face':
                frame = camera_manager.get_face_frame()
            else:
                break

            if frame is not None:
                # Resize frame để tối ưu bandwidth
                if camera_type == 'card':
                    frame = cv2.resize(frame, (640, 480))
                else:
                    frame = cv2.resize(frame, (480, 360))

                # Encode frame thành JPEG với quality tối ưu
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 85]
                ret, buffer = cv2.imencode('.jpg', frame, encode_param)

                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                else:
                    # Fallback frame
                    fallback_frame = create_black_frame(f"Encoding failed for {camera_type}")
                    ret, buffer = cv2.imencode('.jpg', fallback_frame)
                    if ret:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            else:
                # Tạo frame thông báo khi không có camera
                status_text = f"Camera {camera_type} not available"
                if camera_manager.camera_mode == 'single':
                    status_text = f"Single camera mode - {camera_type} view"

                status_frame = create_black_frame(status_text)
                ret, buffer = cv2.imencode('.jpg', status_frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            # Control frame rate
            time.sleep(0.1)  # 10 FPS để giảm tải
            frame_count += 1

        except Exception as e:
            print(f"❌ Error in generate_frames for {camera_type}: {e}")
            # Tạo frame lỗi
            error_frame = create_black_frame(f"Error: {camera_type}")
            try:
                ret, buffer = cv2.imencode('.jpg', error_frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            except:
                pass
            time.sleep(1)

def create_black_frame(text="No Camera"):
    """Tạo frame đen với text"""
    import numpy as np
    frame = np.zeros((480, 640, 3), dtype=np.uint8)

    # Thêm background
    cv2.rectangle(frame, (0, 0), (640, 480), (30, 30, 30), -1)

    # Thêm text với font size phù hợp
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8
    color = (255, 255, 255)
    thickness = 2

    # Tính toán vị trí text để center
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (640 - text_size[0]) // 2
    text_y = (480 + text_size[1]) // 2

    cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness)

    # Thêm timestamp
    import datetime
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    cv2.putText(frame, timestamp, (10, 30), font, 0.5, (128, 128, 128), 1)

    return frame

@app.route('/video_feed_card')
def video_feed_card():
    """Video streaming route cho camera card visit"""
    return Response(generate_frames('card'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/video_feed_face')
def video_feed_face():
    """Video streaming route cho camera face"""
    return Response(generate_frames('face'),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    """Điều chỉnh focus cho camera card visit"""
    data = request.get_json()
    focus_value = data.get('focus_value', 100)
    result = camera_manager.adjust_focus(focus_value)
    return jsonify({'success': result is not None, 'focus_value': result})

@app.route('/capture_card', methods=['POST'])
def capture_card():
    """Chụp ảnh từ camera card visit"""
    filepath = camera_manager.capture_card_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture card image'})

@app.route('/capture_face', methods=['POST'])
def capture_face():
    """Chụp ảnh từ camera face"""
    filepath = camera_manager.capture_face_image()
    if filepath:
        return jsonify({'success': True, 'filepath': filepath})
    return jsonify({'success': False, 'error': 'Failed to capture face image'})

@app.route('/process_and_generate', methods=['POST'])
def process_and_generate():
    """Xử lý OCR và tạo ảnh AI từ 2 ảnh đã chụp"""
    try:
        data = request.get_json()
        card_filepath = data.get('card_filepath')
        face_filepath = data.get('face_filepath')

        if not card_filepath or not face_filepath:
            return jsonify({'success': False, 'error': 'Missing image filepaths'})

        # Bước 1: OCR business card
        print("Processing OCR...")
        card_info = ocr_processor.extract_card_info(card_filepath)
        if not card_info:
            return jsonify({'success': False, 'error': 'Failed to extract card information'})

        # Bước 2: Tạo ảnh AI
        print("Generating AI images...")
        result = ai_generator.generate_dollhouse_images(
            face_image_path=face_filepath,
            card_info=card_info
        )

        if result['success']:
            # Tạo session để lưu kết quả
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_folder = os.path.join(SESSIONS_FOLDER, timestamp)
            os.makedirs(session_folder, exist_ok=True)

            # Lưu thông tin session
            session_data = {
                'timestamp': timestamp,
                'card_info': card_info,
                'card_image': card_filepath,
                'face_image': face_filepath,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            }

            session_file = os.path.join(session_folder, 'session_data.json')
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            return jsonify({
                'success': True,
                'session_id': timestamp,
                'card_info': card_info,
                'generated_images': result['image_paths'],
                'card_images': result.get('card_paths', [])
            })
        else:
            return jsonify({'success': False, 'error': result.get('error', 'Failed to generate images')})

    except Exception as e:
        print(f"Error in process_and_generate: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_session/<session_id>')
def get_session(session_id):
    """Lấy thông tin session"""
    try:
        session_file = os.path.join(SESSIONS_FOLDER, session_id, 'session_data.json')
        if os.path.exists(session_file):
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            return jsonify({'success': True, 'data': session_data})
        else:
            return jsonify({'success': False, 'error': 'Session not found'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Serve uploaded files để preview
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route('/outputs/<filename>')
def output_file(filename):
    return send_file(os.path.join(OUTPUT_FOLDER, filename))

@app.route('/download/<path:filename>')
def download_file(filename):
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Cleanup function
@app.teardown_appcontext
def cleanup_cameras(error=None):
    """Giải phóng camera resources khi app shutdown"""
    camera_manager.release_cameras()

if __name__ == '__main__':
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    finally:
        camera_manager.release_cameras()
