"""
Face Processor - Generate AI faces from real photos using OpenAI DALL-E 3
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os
from datetime import datetime
from typing import Optional, Tuple
import asyncio
import openai
import requests
import base64
import io
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class FaceProcessor:
    def __init__(self):
        """Initialize face processor with OpenAI integration"""
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )

        # AI API setup
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.huggingface_token = os.getenv('HUGGINGFACE_TOKEN')

        # Priority: OpenAI > Hugging Face > Basic Processing
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.ai_provider = 'openai'
            print("🎨 Using OpenAI DALL-E 3 for AI generation")
        elif self.huggingface_token:
            self.ai_provider = 'huggingface'
            print("🤗 Using Hugging Face for AI generation (FREE)")
        else:
            self.ai_provider = 'basic'
            print("⚠️ No AI API found. Using basic image processing.")

        # Style configurations for DALL-E prompts
        self.styles = {
            'anime': {
                'description': 'Anime/manga style portrait with large expressive eyes, smooth skin, and vibrant colors',
                'prompt_template': 'anime style portrait of {description}, large eyes, smooth features, vibrant colors, high quality digital art',
                'fallback_filters': ['smooth', 'brighten', 'saturate']
            },
            'cartoon': {
                'description': 'Cartoon style portrait with simplified features and bold colors',
                'prompt_template': 'cartoon style portrait of {description}, simplified features, bold colors, clean lines, digital illustration',
                'fallback_filters': ['smooth', 'edge_enhance', 'saturate']
            },
            'realistic': {
                'description': 'Professional realistic portrait with enhanced details',
                'prompt_template': 'professional realistic portrait of {description}, high quality, detailed, studio lighting, photorealistic',
                'fallback_filters': ['sharpen', 'brighten']
            },
            'artistic': {
                'description': 'Artistic painting style portrait with creative interpretation',
                'prompt_template': 'artistic painting style portrait of {description}, oil painting, creative interpretation, masterpiece',
                'fallback_filters': ['smooth', 'saturate', 'contrast']
            }
        }
    
    def detect_face(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        Detect face in image
        
        Args:
            image: Input image
            
        Returns:
            Face bounding box (x, y, w, h) or None
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, minSize=(100, 100)
        )
        
        if len(faces) > 0:
            # Return the largest face
            largest_face = max(faces, key=lambda x: x[2] * x[3])
            return tuple(largest_face)
        return None
    
    def crop_face(self, image: np.ndarray, padding: float = 0.3) -> Optional[np.ndarray]:
        """
        Crop face from image with padding
        
        Args:
            image: Input image
            padding: Padding around face (0.3 = 30% padding)
            
        Returns:
            Cropped face image or None
        """
        face_bbox = self.detect_face(image)
        if not face_bbox:
            return None
        
        x, y, w, h = face_bbox
        
        # Add padding
        pad_w = int(w * padding)
        pad_h = int(h * padding)
        
        # Calculate crop coordinates
        x1 = max(0, x - pad_w)
        y1 = max(0, y - pad_h)
        x2 = min(image.shape[1], x + w + pad_w)
        y2 = min(image.shape[0], y + h + pad_h)
        
        return image[y1:y2, x1:x2]
    
    def apply_anime_style(self, image: Image.Image) -> Image.Image:
        """Apply anime-style filters"""
        # Smooth the image
        smoothed = image.filter(ImageFilter.SMOOTH_MORE)
        
        # Enhance colors
        enhancer = ImageEnhance.Color(smoothed)
        colored = enhancer.enhance(1.3)
        
        # Brighten
        enhancer = ImageEnhance.Brightness(colored)
        brightened = enhancer.enhance(1.1)
        
        # Increase contrast slightly
        enhancer = ImageEnhance.Contrast(brightened)
        contrasted = enhancer.enhance(1.1)
        
        return contrasted
    
    def apply_cartoon_style(self, image: Image.Image) -> Image.Image:
        """Apply cartoon-style filters"""
        # Convert to numpy for edge detection
        img_array = np.array(image)
        
        # Apply bilateral filter for smoothing
        smooth = cv2.bilateralFilter(img_array, 15, 80, 80)
        
        # Create edge mask
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        edges = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, 
                                     cv2.THRESH_BINARY, 7, 7)
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        
        # Combine smooth image with edges
        cartoon = cv2.bitwise_and(smooth, edges)
        
        # Convert back to PIL
        result = Image.fromarray(cartoon)
        
        # Enhance saturation
        enhancer = ImageEnhance.Color(result)
        result = enhancer.enhance(1.4)
        
        return result
    
    def apply_realistic_style(self, image: Image.Image) -> Image.Image:
        """Apply realistic enhancement"""
        # Sharpen
        sharpened = image.filter(ImageFilter.SHARPEN)
        
        # Enhance brightness slightly
        enhancer = ImageEnhance.Brightness(sharpened)
        brightened = enhancer.enhance(1.05)
        
        # Enhance contrast
        enhancer = ImageEnhance.Contrast(brightened)
        contrasted = enhancer.enhance(1.1)
        
        return contrasted
    
    def apply_artistic_style(self, image: Image.Image) -> Image.Image:
        """Apply artistic painting style"""
        # Smooth for painting effect
        smoothed = image.filter(ImageFilter.SMOOTH_MORE)
        smoothed = smoothed.filter(ImageFilter.SMOOTH)
        
        # Enhance colors dramatically
        enhancer = ImageEnhance.Color(smoothed)
        colored = enhancer.enhance(1.5)
        
        # Increase contrast for dramatic effect
        enhancer = ImageEnhance.Contrast(colored)
        contrasted = enhancer.enhance(1.3)
        
        # Slight brightness adjustment
        enhancer = ImageEnhance.Brightness(contrasted)
        result = enhancer.enhance(1.1)
        
        return result
    
    def analyze_face_features(self, image: np.ndarray) -> str:
        """
        Analyze face features to create description for AI generation

        Args:
            image: Input face image

        Returns:
            Description string for AI prompt
        """
        # Basic analysis - in real implementation, you could use face analysis APIs
        # For now, we'll create a generic description

        face_bbox = self.detect_face(image)
        if not face_bbox:
            return "a person"

        # Extract basic features (this is simplified - could be enhanced with ML models)
        x, y, w, h = face_bbox
        face_region = image[y:y+h, x:x+w]

        # Analyze brightness (simple proxy for skin tone)
        avg_brightness = np.mean(cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY))

        # Create description based on analysis
        if avg_brightness > 150:
            skin_tone = "light skin"
        elif avg_brightness > 100:
            skin_tone = "medium skin"
        else:
            skin_tone = "dark skin"

        # Analyze face shape (simplified)
        aspect_ratio = w / h
        if aspect_ratio > 0.8:
            face_shape = "round face"
        else:
            face_shape = "oval face"

        return f"a person with {skin_tone}, {face_shape}"

    def encode_image_to_base64(self, image_path: str) -> str:
        """
        Encode image to base64 for API upload

        Args:
            image_path: Path to image file

        Returns:
            Base64 encoded image string
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    async def generate_with_openai(self, face_image_path: str, style: str) -> str:
        """
        Generate AI face using OpenAI DALL-E 3

        Args:
            face_image_path: Path to input face image
            style: Style to apply

        Returns:
            Path to generated AI face image
        """
        try:
            # Load and analyze the face
            image = cv2.imread(face_image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {face_image_path}")

            # Analyze face features
            face_description = self.analyze_face_features(image)

            # Get style configuration
            style_config = self.styles.get(style, self.styles['anime'])

            # Create prompt
            prompt = style_config['prompt_template'].format(description=face_description)

            # Add quality and style modifiers
            prompt += ", high quality, detailed, professional artwork"

            print(f"🎨 Generating AI image with prompt: {prompt}")

            # Call OpenAI API
            response = openai.Image.create(
                prompt=prompt,
                n=1,
                size="1024x1024",
                quality="hd",
                style="vivid"
            )

            # Download the generated image
            image_url = response.data[0].url
            image_response = requests.get(image_url)

            if image_response.status_code == 200:
                # Save the image
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"temp/ai_face_openai_{style}_{timestamp}.png"

                os.makedirs("temp", exist_ok=True)

                with open(output_path, 'wb') as f:
                    f.write(image_response.content)

                # Resize for card use
                ai_image = Image.open(output_path)
                resized_image = self.resize_for_card(ai_image)
                resized_image.save(output_path, "PNG", quality=95)

                print(f"✅ OpenAI generation successful: {output_path}")
                return output_path
            else:
                raise Exception(f"Failed to download generated image: {image_response.status_code}")

        except Exception as e:
            print(f"❌ OpenAI generation failed: {e}")
            raise Exception(f"OpenAI generation error: {str(e)}")

    async def generate_with_huggingface(self, face_image_path: str, style: str) -> str:
        """
        Generate AI face using Hugging Face Inference API (FREE)

        Args:
            face_image_path: Path to input face image
            style: Style to apply

        Returns:
            Path to generated AI face image
        """
        try:
            # Load and analyze the face
            image = cv2.imread(face_image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {face_image_path}")

            # Analyze face features
            face_description = self.analyze_face_features(image)

            # Get style configuration
            style_config = self.styles.get(style, self.styles['anime'])

            # Create prompt
            prompt = style_config['prompt_template'].format(description=face_description)

            print(f"🤗 Generating with Hugging Face: {prompt}")

            # Hugging Face API endpoints (free models)
            models = {
                'anime': 'runwayml/stable-diffusion-v1-5',
                'cartoon': 'nitrosocke/Arcane-Diffusion',
                'realistic': 'stabilityai/stable-diffusion-2-1',
                'artistic': 'dreamlike-art/dreamlike-diffusion-1.0'
            }

            model_id = models.get(style, models['anime'])
            api_url = f"https://api-inference.huggingface.co/models/{model_id}"

            headers = {
                "Authorization": f"Bearer {self.huggingface_token}",
                "Content-Type": "application/json"
            }

            payload = {
                "inputs": prompt,
                "parameters": {
                    "num_inference_steps": 20,
                    "guidance_scale": 7.5,
                    "width": 512,
                    "height": 512
                }
            }

            # Call Hugging Face API
            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code == 200:
                # Save the generated image
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"temp/ai_face_hf_{style}_{timestamp}.png"

                os.makedirs("temp", exist_ok=True)

                with open(output_path, 'wb') as f:
                    f.write(response.content)

                # Resize for card use
                ai_image = Image.open(output_path)
                resized_image = self.resize_for_card(ai_image)
                resized_image.save(output_path, "PNG", quality=95)

                print(f"✅ Hugging Face generation successful: {output_path}")
                return output_path

            elif response.status_code == 503:
                # Model loading, retry after delay
                print("🔄 Model loading, retrying in 10 seconds...")
                await asyncio.sleep(10)
                return await self.generate_with_huggingface(face_image_path, style)

            else:
                error_msg = response.json().get('error', 'Unknown error')
                raise Exception(f"Hugging Face API error: {error_msg}")

        except Exception as e:
            print(f"❌ Hugging Face generation failed: {e}")
            raise Exception(f"Hugging Face generation error: {str(e)}")

    async def generate_with_local_sd(self, face_image_path: str, style: str) -> str:
        """
        Generate AI face using local Stable Diffusion (if available)

        Args:
            face_image_path: Path to input face image
            style: Style to apply

        Returns:
            Path to generated AI face image
        """
        try:
            # Try to import diffusers
            from diffusers import StableDiffusionPipeline
            import torch

            # Load and analyze the face
            image = cv2.imread(face_image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {face_image_path}")

            # Analyze face features
            face_description = self.analyze_face_features(image)

            # Get style configuration
            style_config = self.styles.get(style, self.styles['anime'])
            prompt = style_config['prompt_template'].format(description=face_description)

            print(f"🏠 Generating with local Stable Diffusion: {prompt}")

            # Load model (cache after first use)
            if not hasattr(self, '_sd_pipeline'):
                model_id = "runwayml/stable-diffusion-v1-5"
                device = "cuda" if torch.cuda.is_available() else "cpu"

                self._sd_pipeline = StableDiffusionPipeline.from_pretrained(
                    model_id,
                    torch_dtype=torch.float16 if device == "cuda" else torch.float32
                ).to(device)

            # Generate image
            with torch.no_grad():
                result = self._sd_pipeline(
                    prompt,
                    num_inference_steps=20,
                    guidance_scale=7.5,
                    width=512,
                    height=512
                )

            # Save result
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"temp/ai_face_local_{style}_{timestamp}.png"

            os.makedirs("temp", exist_ok=True)

            # Resize for card use
            resized_image = self.resize_for_card(result.images[0])
            resized_image.save(output_path, "PNG", quality=95)

            print(f"✅ Local SD generation successful: {output_path}")
            return output_path

        except ImportError:
            raise Exception("Local Stable Diffusion not available. Install: pip install diffusers torch")
        except Exception as e:
            print(f"❌ Local SD generation failed: {e}")
            raise Exception(f"Local SD generation error: {str(e)}")

    def resize_for_card(self, image: Image.Image, target_size: Tuple[int, int] = (300, 400)) -> Image.Image:
        """
        Resize image for card use
        
        Args:
            image: Input image
            target_size: Target size (width, height)
            
        Returns:
            Resized image
        """
        # Calculate aspect ratio
        aspect_ratio = image.width / image.height
        target_aspect = target_size[0] / target_size[1]
        
        if aspect_ratio > target_aspect:
            # Image is wider, fit by height
            new_height = target_size[1]
            new_width = int(new_height * aspect_ratio)
        else:
            # Image is taller, fit by width
            new_width = target_size[0]
            new_height = int(new_width / aspect_ratio)
        
        # Resize
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Crop to exact target size if needed
        if (new_width, new_height) != target_size:
            left = (new_width - target_size[0]) // 2
            top = (new_height - target_size[1]) // 2
            right = left + target_size[0]
            bottom = top + target_size[1]
            resized = resized.crop((left, top, right, bottom))
        
        return resized
    
    async def generate_ai_face(self, face_image_path: str, style: str = 'anime') -> str:
        """
        Generate AI-styled face from input photo

        Args:
            face_image_path: Path to input face image
            style: Style to apply ('anime', 'cartoon', 'realistic', 'artistic')

        Returns:
            Path to generated AI face image
        """
        try:
            # Validate style
            if style not in self.styles:
                style = 'anime'

            # Try AI providers in priority order
            if self.ai_provider == 'openai':
                try:
                    return await self.generate_with_openai(face_image_path, style)
                except Exception as e:
                    print(f"⚠️ OpenAI failed, trying Hugging Face: {e}")
                    if self.huggingface_token:
                        try:
                            return await self.generate_with_huggingface(face_image_path, style)
                        except Exception as e2:
                            print(f"⚠️ Hugging Face failed, falling back to basic: {e2}")

            elif self.ai_provider == 'huggingface':
                try:
                    return await self.generate_with_huggingface(face_image_path, style)
                except Exception as e:
                    print(f"⚠️ Hugging Face failed, trying local SD: {e}")
                    try:
                        return await self.generate_with_local_sd(face_image_path, style)
                    except Exception as e2:
                        print(f"⚠️ Local SD failed, falling back to basic: {e2}")

            # Fallback to basic image processing
            print(f"🔄 Using basic image processing for {style} style")

            # Load image
            image = cv2.imread(face_image_path)
            if image is None:
                raise ValueError(f"Cannot load image: {face_image_path}")

            # Crop face
            face_crop = self.crop_face(image)
            if face_crop is None:
                raise ValueError("No face detected in image")

            # Convert to PIL
            face_pil = Image.fromarray(cv2.cvtColor(face_crop, cv2.COLOR_BGR2RGB))

            # Apply style using basic filters
            style_config = self.styles.get(style, self.styles['anime'])
            filters = style_config.get('fallback_filters', ['smooth'])

            styled_face = face_pil
            for filter_name in filters:
                if filter_name == 'smooth':
                    styled_face = styled_face.filter(ImageFilter.SMOOTH_MORE)
                elif filter_name == 'brighten':
                    enhancer = ImageEnhance.Brightness(styled_face)
                    styled_face = enhancer.enhance(1.1)
                elif filter_name == 'saturate':
                    enhancer = ImageEnhance.Color(styled_face)
                    styled_face = enhancer.enhance(1.3)
                elif filter_name == 'sharpen':
                    styled_face = styled_face.filter(ImageFilter.SHARPEN)
                elif filter_name == 'contrast':
                    enhancer = ImageEnhance.Contrast(styled_face)
                    styled_face = enhancer.enhance(1.2)

            # Resize for card use
            final_face = self.resize_for_card(styled_face)

            # Generate output path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"temp/ai_face_basic_{style}_{timestamp}.png"

            # Ensure directory exists
            os.makedirs("temp", exist_ok=True)

            # Save result
            final_face.save(output_path, "PNG", quality=95)

            return output_path

        except Exception as e:
            raise Exception(f"Error generating AI face: {str(e)}")
    
    def get_available_styles(self) -> dict:
        """Get available styles and their descriptions"""
        return self.styles
    
    async def batch_generate(self, face_image_path: str, styles: list = None) -> dict:
        """
        Generate multiple styles at once
        
        Args:
            face_image_path: Path to input face image
            styles: List of styles to generate (None for all)
            
        Returns:
            Dictionary mapping style names to output paths
        """
        if styles is None:
            styles = list(self.styles.keys())
        
        results = {}
        for style in styles:
            try:
                output_path = await self.generate_ai_face(face_image_path, style)
                results[style] = output_path
            except Exception as e:
                results[style] = f"Error: {str(e)}"
        
        return results
