"""
Camera Manager - Handle laptop camera for face capture
"""

import cv2
import numpy as np
import os
from datetime import datetime
from typing import Optional, Tuple
import asyncio
import threading

class CameraManager:
    def __init__(self, camera_index: int = 0):
        """
        Initialize camera manager
        
        Args:
            camera_index: Camera index (0 for default laptop camera)
        """
        self.camera_index = camera_index
        self.cap = None
        self.is_recording = False
        
        # Face detection setup
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        
    def is_camera_available(self) -> bool:
        """Check if camera is available"""
        try:
            cap = cv2.VideoCapture(self.camera_index)
            if cap.isOpened():
                ret, _ = cap.read()
                cap.release()
                return ret
            return False
        except Exception:
            return False
    
    def start_camera(self) -> bool:
        """Start camera capture"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            return True
        except Exception as e:
            print(f"Error starting camera: {e}")
            return False
    
    def stop_camera(self):
        """Stop camera capture"""
        if self.cap:
            self.cap.release()
            self.cap = None
        cv2.destroyAllWindows()
    
    def detect_faces(self, frame: np.ndarray) -> list:
        """
        Detect faces in frame
        
        Args:
            frame: Input frame
            
        Returns:
            List of face rectangles (x, y, w, h)
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(100, 100)
        )
        return faces
    
    def enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality
        
        Args:
            image: Input image
            
        Returns:
            Enhanced image
        """
        # Convert to LAB color space
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge channels and convert back
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    def capture_frame(self) -> Optional[np.ndarray]:
        """Capture single frame from camera"""
        if not self.cap or not self.cap.isOpened():
            if not self.start_camera():
                return None
        
        ret, frame = self.cap.read()
        if ret:
            return frame
        return None
    
    async def capture_face(self, save_path: str = None) -> str:
        """
        Capture face photo with preview and face detection
        
        Args:
            save_path: Optional custom save path
            
        Returns:
            Path to saved image
        """
        if not self.start_camera():
            raise Exception("Cannot start camera")
        
        try:
            print("Camera started. Press SPACE to capture, ESC to cancel")
            
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    continue
                
                # Flip frame horizontally (mirror effect)
                frame = cv2.flip(frame, 1)
                
                # Detect faces
                faces = self.detect_faces(frame)
                
                # Draw face rectangles
                for (x, y, w, h) in faces:
                    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                    cv2.putText(frame, "Face Detected", (x, y - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # Add instructions
                cv2.putText(frame, "Press SPACE to capture, ESC to cancel",
                          (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                # Show frame
                cv2.imshow("Face Capture", frame)
                
                key = cv2.waitKey(1) & 0xFF
                
                # Capture on SPACE
                if key == ord(' '):
                    if len(faces) > 0:
                        # Enhance image
                        enhanced_frame = self.enhance_image(frame)
                        
                        # Generate filename if not provided
                        if not save_path:
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            save_path = f"temp/face_capture_{timestamp}.jpg"
                        
                        # Ensure directory exists
                        os.makedirs(os.path.dirname(save_path), exist_ok=True)
                        
                        # Save image
                        cv2.imwrite(save_path, enhanced_frame)
                        print(f"Face captured and saved to: {save_path}")
                        break
                    else:
                        print("No face detected! Please position your face in the camera.")
                
                # Cancel on ESC
                elif key == 27:  # ESC key
                    raise Exception("Capture cancelled by user")
            
            return save_path
            
        finally:
            self.stop_camera()
    
    async def capture_face_auto(self, timeout: int = 30) -> str:
        """
        Auto capture face when detected (for API use)
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            Path to saved image
        """
        if not self.start_camera():
            raise Exception("Cannot start camera")
        
        try:
            start_time = datetime.now()
            stable_frames = 0
            required_stable_frames = 10  # Require 10 stable frames
            
            while True:
                # Check timeout
                if (datetime.now() - start_time).seconds > timeout:
                    raise Exception("Face capture timeout")
                
                ret, frame = self.cap.read()
                if not ret:
                    continue
                
                # Flip frame
                frame = cv2.flip(frame, 1)
                
                # Detect faces
                faces = self.detect_faces(frame)
                
                if len(faces) > 0:
                    stable_frames += 1
                    if stable_frames >= required_stable_frames:
                        # Enhance and save
                        enhanced_frame = self.enhance_image(frame)
                        
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        save_path = f"temp/face_auto_{timestamp}.jpg"
                        
                        os.makedirs("temp", exist_ok=True)
                        cv2.imwrite(save_path, enhanced_frame)
                        
                        return save_path
                else:
                    stable_frames = 0
                
                # Small delay
                await asyncio.sleep(0.1)
                
        finally:
            self.stop_camera()
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_camera()
