"""
Card Composer - Create business cards with AI faces and extracted info
"""

from PIL import Image, ImageDraw, ImageFont
import os
from datetime import datetime
from typing import Dict, Tu<PERSON>, Optional
import asyncio

class CardComposer:
    def __init__(self):
        """Initialize card composer"""
        self.card_size = (1050, 600)  # Standard business card size in pixels (3.5" x 2" at 300 DPI)
        self.templates = {
            'modern': {
                'background_color': '#FFFFFF',
                'accent_color': '#2C3E50',
                'text_color': '#34495E',
                'layout': 'left_photo'
            },
            'elegant': {
                'background_color': '#F8F9FA',
                'accent_color': '#8E44AD',
                'text_color': '#2C3E50',
                'layout': 'right_photo'
            },
            'professional': {
                'background_color': '#FFFFFF',
                'accent_color': '#3498DB',
                'text_color': '#2C3E50',
                'layout': 'top_photo'
            },
            'creative': {
                'background_color': '#ECF0F1',
                'accent_color': '#E74C3C',
                'text_color': '#2C3E50',
                'layout': 'center_photo'
            }
        }
        
        # Font sizes
        self.font_sizes = {
            'name': 36,
            'position': 24,
            'company': 28,
            'contact': 18,
            'small': 16
        }
        
        # Try to load fonts (fallback to default if not available)
        self.fonts = self._load_fonts()
    
    def _load_fonts(self) -> Dict:
        """Load fonts with fallbacks"""
        fonts = {}
        
        # Try to load custom fonts, fallback to default
        font_paths = {
            'regular': ['arial.ttf', 'DejaVuSans.ttf'],
            'bold': ['arialbd.ttf', 'DejaVuSans-Bold.ttf'],
            'italic': ['ariali.ttf', 'DejaVuSans-Oblique.ttf']
        }
        
        for font_type, paths in font_paths.items():
            fonts[font_type] = {}
            for size_name, size in self.font_sizes.items():
                font_loaded = False
                for path in paths:
                    try:
                        fonts[font_type][size_name] = ImageFont.truetype(path, size)
                        font_loaded = True
                        break
                    except:
                        continue
                
                if not font_loaded:
                    # Fallback to default font
                    try:
                        fonts[font_type][size_name] = ImageFont.load_default()
                    except:
                        fonts[font_type][size_name] = ImageFont.load_default()
        
        return fonts
    
    def create_background(self, template: str) -> Image.Image:
        """Create card background"""
        config = self.templates.get(template, self.templates['modern'])
        
        # Create base image
        card = Image.new('RGB', self.card_size, config['background_color'])
        draw = ImageDraw.Draw(card)
        
        # Add accent elements
        accent_color = config['accent_color']
        
        if template == 'modern':
            # Left accent bar
            draw.rectangle([0, 0, 10, self.card_size[1]], fill=accent_color)
        elif template == 'elegant':
            # Top accent bar
            draw.rectangle([0, 0, self.card_size[0], 15], fill=accent_color)
        elif template == 'professional':
            # Bottom accent bar
            draw.rectangle([0, self.card_size[1]-15, self.card_size[0], self.card_size[1]], fill=accent_color)
        elif template == 'creative':
            # Corner accent
            points = [(0, 0), (100, 0), (0, 100)]
            draw.polygon(points, fill=accent_color)
        
        return card
    
    def add_photo(self, card: Image.Image, photo_path: str, template: str) -> Image.Image:
        """Add photo to card based on template layout"""
        config = self.templates.get(template, self.templates['modern'])
        layout = config['layout']
        
        # Load and process photo
        try:
            photo = Image.open(photo_path)
        except:
            # Create placeholder if photo can't be loaded
            photo = Image.new('RGB', (200, 250), '#CCCCCC')
            draw = ImageDraw.Draw(photo)
            draw.text((50, 125), "Photo", fill='#666666')
        
        # Resize photo based on layout
        if layout == 'left_photo':
            photo_size = (200, 250)
            photo_pos = (30, 50)
        elif layout == 'right_photo':
            photo_size = (200, 250)
            photo_pos = (self.card_size[0] - 230, 50)
        elif layout == 'top_photo':
            photo_size = (150, 180)
            photo_pos = (50, 30)
        else:  # center_photo
            photo_size = (180, 220)
            photo_pos = ((self.card_size[0] - 180) // 2, 30)
        
        # Resize photo
        photo = photo.resize(photo_size, Image.Resampling.LANCZOS)
        
        # Create rounded corners
        mask = Image.new('L', photo_size, 0)
        mask_draw = ImageDraw.Draw(mask)
        mask_draw.rounded_rectangle([0, 0, photo_size[0], photo_size[1]], 
                                   radius=15, fill=255)
        
        # Apply mask
        photo.putalpha(mask)
        
        # Paste photo on card
        card.paste(photo, photo_pos, photo)
        
        return card
    
    def add_text_info(self, card: Image.Image, info: Dict, template: str) -> Image.Image:
        """Add text information to card"""
        config = self.templates.get(template, self.templates['modern'])
        layout = config['layout']
        text_color = config['text_color']
        accent_color = config['accent_color']
        
        draw = ImageDraw.Draw(card)
        
        # Calculate text area based on layout
        if layout == 'left_photo':
            text_x = 260
            text_y = 50
            text_width = self.card_size[0] - 290
        elif layout == 'right_photo':
            text_x = 30
            text_y = 50
            text_width = self.card_size[0] - 260
        elif layout == 'top_photo':
            text_x = 220
            text_y = 50
            text_width = self.card_size[0] - 250
        else:  # center_photo
            text_x = 30
            text_y = 280
            text_width = self.card_size[0] - 60
        
        current_y = text_y
        
        # Name (largest, bold)
        if info.get('name'):
            font = self.fonts['bold']['name']
            draw.text((text_x, current_y), info['name'], 
                     fill=accent_color, font=font)
            current_y += 50
        
        # Position
        if info.get('position'):
            font = self.fonts['italic']['position']
            draw.text((text_x, current_y), info['position'], 
                     fill=text_color, font=font)
            current_y += 35
        
        # Company
        if info.get('company'):
            font = self.fonts['bold']['company']
            draw.text((text_x, current_y), info['company'], 
                     fill=text_color, font=font)
            current_y += 40
        
        # Contact information
        contact_y = current_y + 20
        
        if info.get('email'):
            font = self.fonts['regular']['contact']
            draw.text((text_x, contact_y), f"📧 {info['email']}", 
                     fill=text_color, font=font)
            contact_y += 25
        
        if info.get('phone'):
            font = self.fonts['regular']['contact']
            draw.text((text_x, contact_y), f"📞 {info['phone']}", 
                     fill=text_color, font=font)
            contact_y += 25
        
        if info.get('website'):
            font = self.fonts['regular']['contact']
            draw.text((text_x, contact_y), f"🌐 {info['website']}", 
                     fill=text_color, font=font)
            contact_y += 25
        
        if info.get('address'):
            font = self.fonts['regular']['small']
            # Wrap address text if too long
            address = info['address']
            if len(address) > 50:
                words = address.split()
                lines = []
                current_line = []
                for word in words:
                    current_line.append(word)
                    if len(' '.join(current_line)) > 50:
                        if len(current_line) > 1:
                            current_line.pop()
                            lines.append(' '.join(current_line))
                            current_line = [word]
                        else:
                            lines.append(word)
                            current_line = []
                if current_line:
                    lines.append(' '.join(current_line))
                
                for line in lines:
                    draw.text((text_x, contact_y), f"📍 {line}", 
                             fill=text_color, font=font)
                    contact_y += 20
            else:
                draw.text((text_x, contact_y), f"📍 {address}", 
                         fill=text_color, font=font)
        
        return card
    
    async def create_card(self, ai_face_path: str, card_info: Dict, template: str = 'modern') -> str:
        """
        Create complete business card
        
        Args:
            ai_face_path: Path to AI-generated face image
            card_info: Extracted card information
            template: Template style to use
            
        Returns:
            Path to created card image
        """
        try:
            # Validate template
            if template not in self.templates:
                template = 'modern'
            
            # Create background
            card = self.create_background(template)
            
            # Add photo
            card = self.add_photo(card, ai_face_path, template)
            
            # Add text information
            card = self.add_text_info(card, card_info, template)
            
            # Generate output path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"outputs/card_{template}_{timestamp}.png"
            
            # Ensure directory exists
            os.makedirs("outputs", exist_ok=True)
            
            # Save card
            card.save(output_path, "PNG", quality=95, dpi=(300, 300))
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Error creating card: {str(e)}")
    
    def get_available_templates(self) -> Dict:
        """Get available templates and their descriptions"""
        return {
            name: {
                'description': f"{name.title()} style card",
                'layout': config['layout'],
                'colors': {
                    'background': config['background_color'],
                    'accent': config['accent_color'],
                    'text': config['text_color']
                }
            }
            for name, config in self.templates.items()
        }
    
    async def create_multiple_templates(self, ai_face_path: str, card_info: Dict) -> Dict[str, str]:
        """
        Create cards with all available templates
        
        Args:
            ai_face_path: Path to AI-generated face image
            card_info: Extracted card information
            
        Returns:
            Dictionary mapping template names to output paths
        """
        results = {}
        for template in self.templates.keys():
            try:
                output_path = await self.create_card(ai_face_path, card_info, template)
                results[template] = output_path
            except Exception as e:
                results[template] = f"Error: {str(e)}"
        
        return results
