"""
AI Card Visit Generator - Main API
"""

from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import os
from pathlib import Path

# Import modules
from camera.camera_manager import CameraManager
from ocr.card_ocr import CardOCR
from ai_generation.face_processor import FaceProcessor
from card_designer.card_composer import CardComposer

# Create FastAPI app
app = FastAPI(
    title="AI Card Visit Generator",
    description="Generate AI-powered business cards with face photos",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# Mount static files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")

# Initialize components
camera_manager = CameraManager()
card_ocr = CardOCR()
face_processor = FaceProcessor()
card_composer = CardComposer()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Card Visit Generator API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "camera_available": camera_manager.is_camera_available(),
        "services": {
            "ocr": "ready",
            "ai_generation": "ready",
            "card_designer": "ready"
        }
    }

@app.post("/capture-face")
async def capture_face():
    """Capture face photo from laptop camera"""
    try:
        image_path = await camera_manager.capture_face()
        return {
            "success": True,
            "image_path": image_path,
            "message": "Face captured successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload-card")
async def upload_card(file: UploadFile = File(...)):
    """Upload card visit image"""
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file
        file_path = f"uploads/card_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {
            "success": True,
            "file_path": file_path,
            "message": "Card uploaded successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/extract-card-info")
async def extract_card_info(file_path: str):
    """Extract information from card visit"""
    try:
        card_info = await card_ocr.extract_info(file_path)
        return {
            "success": True,
            "card_info": card_info,
            "message": "Card information extracted successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate-ai-face")
async def generate_ai_face(face_image_path: str, style: str = "anime"):
    """Generate AI face from captured photo"""
    try:
        ai_face_path = await face_processor.generate_ai_face(face_image_path, style)
        return {
            "success": True,
            "ai_face_path": ai_face_path,
            "message": "AI face generated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/create-card")
async def create_card(
    ai_face_path: str,
    card_info: dict,
    template: str = "modern"
):
    """Create new card visit with AI face and extracted info"""
    try:
        card_path = await card_composer.create_card(
            ai_face_path, card_info, template
        )
        return {
            "success": True,
            "card_path": card_path,
            "message": "Card created successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process-complete")
async def process_complete(
    face_image_path: str,
    card_image_path: str,
    style: str = "anime",
    template: str = "modern"
):
    """Complete processing pipeline"""
    try:
        # Step 1: Extract card info
        card_info = await card_ocr.extract_info(card_image_path)
        
        # Step 2: Generate AI face
        ai_face_path = await face_processor.generate_ai_face(face_image_path, style)
        
        # Step 3: Create new card
        final_card_path = await card_composer.create_card(
            ai_face_path, card_info, template
        )
        
        return {
            "success": True,
            "card_info": card_info,
            "ai_face_path": ai_face_path,
            "final_card_path": final_card_path,
            "message": "Complete processing finished successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
