"""
Card OCR - Extract information from business cards
"""

import cv2
import numpy as np
import re
from typing import Dict, List, Optional
import easyocr
from PIL import Image
import asyncio

class CardOCR:
    def __init__(self):
        """Initialize OCR with Vietnamese and English support"""
        self.reader = easyocr.Reader(['vi', 'en'], gpu=False)
        
        # Regex patterns for information extraction
        self.patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'(\+84|84|0)[\s\-\.]?[1-9][\d\s\-\.]{7,11}',
            'website': r'(https?://)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}',
            'address': r'(số\s*\d+|đ<PERSON>ờng|phườ<PERSON>|quận|huy<PERSON>n|thành phố|tỉnh)',
        }
        
        # Common Vietnamese titles/positions
        self.position_keywords = [
            'giám đốc', 'phó giám đốc', 'trưởng phòng', 'phó phòng',
            'chủ tịch', 'phó chủ tịch', 'tổng giám đốc', 'ceo', 'cto', 'cfo',
            'manager', 'director', 'supervisor', 'leader', 'head',
            'kỹ sư', 'chuyên viên', 'nhân viên', 'thư ký', 'kế toán'
        ]
        
        # Company keywords
        self.company_keywords = [
            'công ty', 'cty', 'company', 'corp', 'corporation', 'ltd',
            'co.', 'inc', 'group', 'tập đoàn', 'doanh nghiệp'
        ]
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """
        Preprocess image for better OCR results
        
        Args:
            image_path: Path to image file
            
        Returns:
            Preprocessed image
        """
        # Read image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Cannot read image: {image_path}")
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def extract_text(self, image_path: str) -> List[Dict]:
        """
        Extract text from image using EasyOCR
        
        Args:
            image_path: Path to image file
            
        Returns:
            List of detected text with coordinates and confidence
        """
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image_path)
            
            # Extract text
            results = self.reader.readtext(processed_image)
            
            # Format results
            formatted_results = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low confidence results
                    formatted_results.append({
                        'text': text.strip(),
                        'confidence': confidence,
                        'bbox': bbox
                    })
            
            return formatted_results
            
        except Exception as e:
            print(f"Error in text extraction: {e}")
            return []
    
    def extract_email(self, text: str) -> Optional[str]:
        """Extract email from text"""
        matches = re.findall(self.patterns['email'], text, re.IGNORECASE)
        return matches[0] if matches else None
    
    def extract_phone(self, text: str) -> Optional[str]:
        """Extract phone number from text"""
        # Clean text first
        cleaned_text = re.sub(r'[^\d\+\-\.\s]', '', text)
        matches = re.findall(self.patterns['phone'], cleaned_text)
        
        if matches:
            # Clean and format phone number
            phone = re.sub(r'[\s\-\.]', '', matches[0])
            # Normalize Vietnamese phone format
            if phone.startswith('84'):
                phone = '+84' + phone[2:]
            elif phone.startswith('0'):
                phone = '+84' + phone[1:]
            return phone
        return None
    
    def extract_website(self, text: str) -> Optional[str]:
        """Extract website from text"""
        matches = re.findall(self.patterns['website'], text, re.IGNORECASE)
        if matches:
            website = matches[0][1] if matches[0][0] else matches[0][1]
            if not website.startswith('http'):
                website = 'https://' + website
            return website
        return None
    
    def identify_name(self, texts: List[str]) -> Optional[str]:
        """
        Identify person name (usually the largest/most prominent text)
        
        Args:
            texts: List of extracted texts
            
        Returns:
            Identified name
        """
        # Filter out emails, phones, websites
        filtered_texts = []
        for text in texts:
            if (not self.extract_email(text) and 
                not self.extract_phone(text) and 
                not self.extract_website(text) and
                len(text.split()) <= 4 and  # Names usually 1-4 words
                len(text) > 2):
                filtered_texts.append(text)
        
        if filtered_texts:
            # Return the first non-company text (assuming name comes first)
            for text in filtered_texts:
                if not any(keyword in text.lower() for keyword in self.company_keywords):
                    return text
            return filtered_texts[0]
        
        return None
    
    def identify_position(self, texts: List[str]) -> Optional[str]:
        """Identify job position/title"""
        for text in texts:
            text_lower = text.lower()
            for keyword in self.position_keywords:
                if keyword in text_lower:
                    return text
        return None
    
    def identify_company(self, texts: List[str]) -> Optional[str]:
        """Identify company name"""
        for text in texts:
            text_lower = text.lower()
            for keyword in self.company_keywords:
                if keyword in text_lower:
                    return text
        
        # If no keyword found, look for longer texts that might be company names
        company_candidates = [text for text in texts if len(text) > 10]
        return company_candidates[0] if company_candidates else None
    
    def identify_address(self, texts: List[str]) -> Optional[str]:
        """Identify address"""
        address_parts = []
        for text in texts:
            if re.search(self.patterns['address'], text, re.IGNORECASE):
                address_parts.append(text)
        
        return ', '.join(address_parts) if address_parts else None
    
    async def extract_info(self, image_path: str) -> Dict:
        """
        Extract all information from business card
        
        Args:
            image_path: Path to card image
            
        Returns:
            Dictionary containing extracted information
        """
        try:
            # Extract text from image
            ocr_results = self.extract_text(image_path)
            
            if not ocr_results:
                return {
                    'success': False,
                    'error': 'No text detected in image'
                }
            
            # Get all text strings
            all_texts = [result['text'] for result in ocr_results]
            full_text = ' '.join(all_texts)
            
            # Extract specific information
            extracted_info = {
                'success': True,
                'name': self.identify_name(all_texts),
                'position': self.identify_position(all_texts),
                'company': self.identify_company(all_texts),
                'email': self.extract_email(full_text),
                'phone': self.extract_phone(full_text),
                'website': self.extract_website(full_text),
                'address': self.identify_address(all_texts),
                'raw_text': all_texts,
                'confidence_scores': [result['confidence'] for result in ocr_results]
            }
            
            return extracted_info
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_extraction(self, info: Dict) -> Dict:
        """
        Validate and clean extracted information
        
        Args:
            info: Extracted information dictionary
            
        Returns:
            Validated and cleaned information
        """
        validated = info.copy()
        
        # Clean and validate email
        if validated.get('email'):
            email = validated['email'].lower().strip()
            if '@' not in email or '.' not in email:
                validated['email'] = None
            else:
                validated['email'] = email
        
        # Clean and validate phone
        if validated.get('phone'):
            phone = re.sub(r'[^\d\+]', '', validated['phone'])
            if len(phone) < 10:
                validated['phone'] = None
            else:
                validated['phone'] = phone
        
        # Clean text fields
        for field in ['name', 'position', 'company', 'address']:
            if validated.get(field):
                validated[field] = validated[field].strip()
        
        return validated
