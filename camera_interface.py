#!/usr/bin/env python3
"""
Camera Interface Module - Modular camera system cho testing và production
"""

import cv2
import os
from abc import ABC, abstractmethod
from pathlib import Path
from datetime import datetime
import tkinter as tk
from tkinter import filedialog
from PIL import Image, ImageTk
import numpy as np

class CameraInterface(ABC):
    """Abstract camera interface cho easy switching"""
    
    @abstractmethod
    def capture_image(self, save_path=None):
        """Capture image và return path"""
        pass
    
    @abstractmethod
    def is_available(self):
        """Check if camera is available"""
        pass

class LaptopFileImporter(CameraInterface):
    """Testing: Import card images từ laptop folder"""
    
    def __init__(self, default_folder="card_imports"):
        self.default_folder = Path(default_folder)
        self.default_folder.mkdir(exist_ok=True)
        print("📁 Laptop File Importer initialized")
    
    def capture_image(self, save_path=None):
        """Import image từ file dialog"""
        try:
            # Open file dialog
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            file_path = filedialog.askopenfilename(
                title="Select Business Card Image",
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                    ("All files", "*.*")
                ]
            )
            
            if not file_path:
                return None
            
            # Copy to save_path if provided
            if save_path:
                import shutil
                shutil.copy2(file_path, save_path)
                return save_path
            
            return file_path
            
        except Exception as e:
            print(f"❌ File import error: {e}")
            return None
    
    def is_available(self):
        return True

class LaptopWebcam(CameraInterface):
    """Testing: Laptop webcam cho face capture"""
    
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.cap = None
        print("📷 Laptop Webcam initialized")
    
    def capture_image(self, save_path=None):
        """Capture từ laptop webcam"""
        try:
            # Initialize camera
            self.cap = cv2.VideoCapture(self.camera_index)
            
            if not self.cap.isOpened():
                print("❌ Cannot open laptop webcam")
                return None
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            
            print("📷 Press SPACE to capture, ESC to cancel")
            
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    break
                
                # Flip frame horizontally (mirror effect)
                frame = cv2.flip(frame, 1)
                
                # Add capture instruction
                cv2.putText(frame, "Press SPACE to capture, ESC to cancel", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                cv2.imshow('Face Capture - Laptop Webcam', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord(' '):  # Space to capture
                    if save_path:
                        cv2.imwrite(save_path, frame)
                        print(f"✅ Face captured: {save_path}")
                        break
                    else:
                        # Generate timestamp filename
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        save_path = f"face_capture_{timestamp}.jpg"
                        cv2.imwrite(save_path, frame)
                        print(f"✅ Face captured: {save_path}")
                        break
                elif key == 27:  # ESC to cancel
                    print("❌ Capture cancelled")
                    save_path = None
                    break
            
            self.cap.release()
            cv2.destroyAllWindows()
            return save_path
            
        except Exception as e:
            print(f"❌ Webcam capture error: {e}")
            if self.cap:
                self.cap.release()
            cv2.destroyAllWindows()
            return None
    
    def is_available(self):
        """Check if webcam is available"""
        try:
            cap = cv2.VideoCapture(self.camera_index)
            available = cap.isOpened()
            cap.release()
            return available
        except:
            return False

class ExternalCamera(CameraInterface):
    """Production: External camera devices"""
    
    def __init__(self, device_id, camera_type="card"):
        self.device_id = device_id
        self.camera_type = camera_type  # "card" or "face"
        self.cap = None
        print(f"🎥 External Camera initialized (Type: {camera_type}, Device: {device_id})")
    
    def capture_image(self, save_path=None):
        """Capture từ external camera"""
        try:
            self.cap = cv2.VideoCapture(self.device_id)
            
            if not self.cap.isOpened():
                print(f"❌ Cannot open external camera {self.device_id}")
                return None
            
            # Set high resolution for card scanning
            if self.camera_type == "card":
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
            else:
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            
            print(f"📷 Press SPACE to capture {self.camera_type}, ESC to cancel")
            
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    break
                
                # Add capture instruction
                instruction = f"Press SPACE to capture {self.camera_type}, ESC to cancel"
                cv2.putText(frame, instruction, 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                cv2.imshow(f'{self.camera_type.title()} Capture - External Camera', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord(' '):  # Space to capture
                    if save_path:
                        cv2.imwrite(save_path, frame)
                        print(f"✅ {self.camera_type.title()} captured: {save_path}")
                        break
                    else:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        save_path = f"{self.camera_type}_capture_{timestamp}.jpg"
                        cv2.imwrite(save_path, frame)
                        print(f"✅ {self.camera_type.title()} captured: {save_path}")
                        break
                elif key == 27:  # ESC to cancel
                    print("❌ Capture cancelled")
                    save_path = None
                    break
            
            self.cap.release()
            cv2.destroyAllWindows()
            return save_path
            
        except Exception as e:
            print(f"❌ External camera error: {e}")
            if self.cap:
                self.cap.release()
            cv2.destroyAllWindows()
            return None
    
    def is_available(self):
        """Check if external camera is available"""
        try:
            cap = cv2.VideoCapture(self.device_id)
            available = cap.isOpened()
            cap.release()
            return available
        except:
            return False

class CameraManager:
    """Camera manager cho easy switching giữa testing và production"""
    
    def __init__(self, mode="testing"):
        self.mode = mode
        self.setup_cameras()
    
    def setup_cameras(self):
        """Setup cameras based on mode"""
        if self.mode == "testing":
            self.card_camera = LaptopFileImporter()
            self.face_camera = LaptopWebcam()
            print("🧪 Testing mode: File import + Laptop webcam")
        
        elif self.mode == "production":
            self.card_camera = ExternalCamera(device_id=1, camera_type="card")
            self.face_camera = ExternalCamera(device_id=2, camera_type="face")
            print("🏭 Production mode: External cameras")
        
        else:
            raise ValueError("Mode must be 'testing' or 'production'")
    
    def capture_card(self, save_path=None):
        """Capture business card"""
        if not save_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"uploads/card_{timestamp}.jpg"
            Path("uploads").mkdir(exist_ok=True)
        
        return self.card_camera.capture_image(save_path)
    
    def capture_face(self, save_path=None):
        """Capture face photo"""
        if not save_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"uploads/face_{timestamp}.jpg"
            Path("uploads").mkdir(exist_ok=True)
        
        return self.face_camera.capture_image(save_path)
    
    def switch_mode(self, new_mode):
        """Switch between testing và production mode"""
        print(f"🔄 Switching from {self.mode} to {new_mode} mode")
        self.mode = new_mode
        self.setup_cameras()
    
    def check_availability(self):
        """Check camera availability"""
        card_available = self.card_camera.is_available()
        face_available = self.face_camera.is_available()
        
        print(f"📷 Card camera: {'✅' if card_available else '❌'}")
        print(f"👤 Face camera: {'✅' if face_available else '❌'}")
        
        return card_available and face_available

def test_camera_system():
    """Test camera system"""
    print("🧪 Testing Camera System")
    print("=" * 50)
    
    # Test testing mode
    manager = CameraManager(mode="testing")
    manager.check_availability()
    
    print("\n📁 Testing card import...")
    # card_path = manager.capture_card()
    # if card_path:
    #     print(f"✅ Card imported: {card_path}")
    
    print("\n📷 Testing face capture...")
    # face_path = manager.capture_face()
    # if face_path:
    #     print(f"✅ Face captured: {face_path}")

if __name__ == "__main__":
    test_camera_system()
