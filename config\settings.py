"""
Configuration settings for AI Business Card Generator
"""
import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent.parent
UPLOADS_DIR = BASE_DIR / "uploads"
OUTPUTS_DIR = BASE_DIR / "outputs"
TEMPLATES_DIR = BASE_DIR / "templates"
STATIC_DIR = BASE_DIR / "static"

# Create directories if they don't exist
for dir_path in [UPLOADS_DIR, OUTPUTS_DIR, TEMPLATES_DIR, STATIC_DIR]:
    dir_path.mkdir(exist_ok=True)

# API Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
HUGGINGFACE_API_KEY = os.getenv('HUGGINGFACE_API_KEY', '')
REPLICATE_API_KEY = os.getenv('REPLICATE_API_KEY', '')

# AI Generation Settings
AI_GENERATION_CONFIG = {
    'openai': {
        'model': 'dall-e-3',
        'size': '1024x1024',
        'quality': 'standard',
        'style': 'vivid',
        'timeout': 60
    },
    'huggingface': {
        'model': 'stabilityai/stable-diffusion-xl-base-1.0',
        'steps': 20,
        'guidance_scale': 7.5,
        'timeout': 30
    },
    'replicate': {
        'model': 'stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b',
        'timeout': 60
    }
}

# Image Processing Settings
IMAGE_SETTINGS = {
    'max_upload_size': 10 * 1024 * 1024,  # 10MB
    'allowed_extensions': {'.jpg', '.jpeg', '.png', '.gif'},
    'face_detection_confidence': 0.5,
    'output_quality': 85
}

# Anime Style Prompts
ANIME_PROMPTS = {
    'base': """anime portrait of a professional person, manga style, beautiful detailed eyes, 
    soft lighting, clean background, high quality digital art, studio ghibli style, 
    professional business portrait, detailed facial features, soft colors, elegant composition, 4k resolution""",
    
    'business': """professional anime business portrait, manga art style, corporate setting, 
    clean suit, confident expression, detailed eyes, soft studio lighting, 
    high quality digital illustration, elegant composition""",
    
    'creative': """creative anime portrait, artistic manga style, vibrant colors, 
    dynamic composition, detailed character design, professional quality, 
    studio lighting, beautiful background"""
}

# Card Processing Settings
CARD_PROCESSING = {
    'ocr_confidence': 0.6,
    'text_extraction_languages': ['eng', 'vie'],
    'card_template_size': (1050, 600),
    'portrait_size': (400, 400)
}

# Flask Settings
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5002,
    'debug': True
}
