#!/usr/bin/env python3
"""
Convert Scientific Report from Markdown to Word Document
Chuyển đổi <PERSON><PERSON> c<PERSON><PERSON> học từ Markdown sang Word Document
"""

import os
import sys
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import re

class MarkdownToWordConverter:
    """Convert Markdown scientific report to Word document"""
    
    def __init__(self):
        self.doc = Document()
        self.setup_styles()
        print("📄 Word Document Converter initialized")
    
    def setup_styles(self):
        """Setup custom styles for the document"""
        try:
            # Title style
            title_style = self.doc.styles.add_style('Custom Title', WD_STYLE_TYPE.PARAGRAPH)
            title_font = title_style.font
            title_font.name = 'Times New Roman'
            title_font.size = Pt(16)
            title_font.bold = True
            title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_style.paragraph_format.space_after = Pt(12)
            
            # Heading styles
            for level in range(1, 4):
                style_name = f'Custom Heading {level}'
                heading_style = self.doc.styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
                heading_font = heading_style.font
                heading_font.name = 'Times New Roman'
                heading_font.size = Pt(14 - level)
                heading_font.bold = True
                heading_style.paragraph_format.space_before = Pt(12)
                heading_style.paragraph_format.space_after = Pt(6)
            
            # Body text style
            body_style = self.doc.styles.add_style('Custom Body', WD_STYLE_TYPE.PARAGRAPH)
            body_font = body_style.font
            body_font.name = 'Times New Roman'
            body_font.size = Pt(12)
            body_style.paragraph_format.line_spacing = 1.15
            body_style.paragraph_format.space_after = Pt(6)
            
            # Code style
            code_style = self.doc.styles.add_style('Custom Code', WD_STYLE_TYPE.PARAGRAPH)
            code_font = code_style.font
            code_font.name = 'Courier New'
            code_font.size = Pt(10)
            code_style.paragraph_format.left_indent = Inches(0.5)
            
            print("✅ Document styles configured")
            
        except Exception as e:
            print(f"⚠️ Style setup warning: {e}")
    
    def convert_markdown_to_word(self, markdown_file, output_file):
        """Convert markdown file to Word document"""
        try:
            print(f"📖 Reading markdown file: {markdown_file}")
            
            with open(markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Process content line by line
            lines = content.split('\n')
            in_code_block = False
            code_content = []
            
            for line in lines:
                line = line.rstrip()
                
                # Handle code blocks
                if line.startswith('```'):
                    if in_code_block:
                        # End of code block
                        if code_content:
                            code_text = '\n'.join(code_content)
                            self.add_code_block(code_text)
                        code_content = []
                        in_code_block = False
                    else:
                        # Start of code block
                        in_code_block = True
                    continue
                
                if in_code_block:
                    code_content.append(line)
                    continue
                
                # Process regular content
                self.process_line(line)
            
            # Save document
            print(f"💾 Saving Word document: {output_file}")
            self.doc.save(output_file)
            print(f"✅ Successfully converted to: {output_file}")
            
        except Exception as e:
            print(f"❌ Conversion error: {e}")
            raise
    
    def process_line(self, line):
        """Process individual line of markdown"""
        if not line.strip():
            # Empty line
            self.doc.add_paragraph()
            return
        
        # Headers
        if line.startswith('#'):
            level = len(line) - len(line.lstrip('#'))
            text = line.lstrip('# ').strip()
            
            if level == 1:
                # Main title
                p = self.doc.add_paragraph(text, style='Custom Title')
            elif level <= 3:
                p = self.doc.add_paragraph(text, style=f'Custom Heading {level}')
            else:
                p = self.doc.add_paragraph(text, style='Custom Body')
                p.runs[0].bold = True
            return
        
        # Horizontal rules
        if line.strip() == '---':
            p = self.doc.add_paragraph()
            p.add_run('_' * 50)
            return
        
        # Tables
        if '|' in line and line.count('|') >= 2:
            self.process_table_line(line)
            return
        
        # Lists
        if line.startswith(('- ', '* ', '+ ')):
            text = line[2:].strip()
            p = self.doc.add_paragraph(text, style='List Bullet')
            return
        
        if re.match(r'^\d+\.\s', line):
            text = re.sub(r'^\d+\.\s', '', line)
            p = self.doc.add_paragraph(text, style='List Number')
            return
        
        # Regular paragraph
        self.add_formatted_paragraph(line)
    
    def add_formatted_paragraph(self, text):
        """Add paragraph with formatting (bold, italic, etc.)"""
        p = self.doc.add_paragraph(style='Custom Body')
        
        # Simple formatting - can be enhanced
        if '**' in text:
            parts = text.split('**')
            for i, part in enumerate(parts):
                if i % 2 == 0:
                    p.add_run(part)
                else:
                    p.add_run(part).bold = True
        else:
            p.add_run(text)
    
    def add_code_block(self, code_text):
        """Add code block to document"""
        p = self.doc.add_paragraph(code_text, style='Custom Code')
        # Add border (simplified)
        p.paragraph_format.left_indent = Inches(0.5)
        p.paragraph_format.right_indent = Inches(0.5)
    
    def process_table_line(self, line):
        """Process table lines (simplified implementation)"""
        # This is a simplified table processor
        # For full table support, more complex parsing would be needed
        cells = [cell.strip() for cell in line.split('|') if cell.strip()]
        if cells:
            p = self.doc.add_paragraph(' | '.join(cells), style='Custom Body')

def main():
    """Main conversion function"""
    print("🔄 Starting Markdown to Word conversion...")
    print("=" * 60)
    
    # File paths
    markdown_file = Path("SCIENTIFIC_REPORT_AI_CARD_VISIT.md")
    output_file = Path("AI_CARD_VISIT_SCIENTIFIC_REPORT.docx")
    
    # Check if markdown file exists
    if not markdown_file.exists():
        print(f"❌ Markdown file not found: {markdown_file}")
        return
    
    try:
        # Create converter and convert
        converter = MarkdownToWordConverter()
        converter.convert_markdown_to_word(markdown_file, output_file)
        
        print("\n" + "=" * 60)
        print("✅ CONVERSION COMPLETED SUCCESSFULLY!")
        print(f"📄 Input:  {markdown_file}")
        print(f"📄 Output: {output_file}")
        print(f"📊 Size:   {output_file.stat().st_size / 1024:.1f} KB")
        
        # Open file automatically (Windows)
        if os.name == 'nt':
            try:
                os.startfile(str(output_file))
                print("📖 Opening Word document...")
            except:
                print("💡 Please open the Word document manually")
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return

if __name__ == "__main__":
    main()
