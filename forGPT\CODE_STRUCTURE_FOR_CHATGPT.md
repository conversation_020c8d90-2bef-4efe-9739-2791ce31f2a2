# AI Card Visit - Code Structure Summary for ChatGPT

## 🏗️ MAIN APPLICATION STRUCTURE

### 1. Flask App (`ai_card_visit_app.py`)
```python
# Main routes:
@app.route('/')                    # Home page
@app.route('/upload_card')         # Card upload & OCR
@app.route('/verify_info')         # Verify extracted info
@app.route('/capture_face')        # Face capture
@app.route('/generate_ai')         # AI image generation
@app.route('/download_image')      # Download results

# Key initialization:
workflow_controller = WorkflowController()
```

### 2. AI Generator (`ai_generator.py`)
```python
class AIImageGenerator:
    def __init__(self):
        self.model_name = "gemini-2.0-flash-preview-image-generation"
        self.client = genai.Client(api_key=api_key)
    
    # Main methods:
    def generate_dollhouse_image(face_path, card_info)  # Generate 2 AI images
    def _create_dollhouse_prompt(face_analysis, card_info)  # Create prompt
    def _burn_text_into_image(image_path, card_info)  # Add text overlay
    def _ultra_face_analysis(face_path, card_info)  # Analyze face
```

### 3. OCR Service (`ocr_service.py`)
```python
class OCRService:
    def __init__(self):
        self.model_name = "gemini-2.5-flash"
    
    def extract_card_info(image_path):
        # Returns: {name, title, company, email, phone, address}
```

### 4. Camera Manager (`camera_manager.py`)
```python
class CameraManager:
    def __init__(self):
        self.laptop_webcam = LaptopWebcam()
        self.file_importer = LaptopFileImporter()
    
    def capture_face():  # Capture from webcam or file
```

## 🎨 AI IMAGE GENERATION FLOW

### Current Prompt Strategy:
```python
def _create_dollhouse_prompt(self, face_analysis, card_info):
    prompt = f"""
    CRITICAL: Create dollhouse scene matching model1.png EXACTLY
    
    CAMERA POSITIONING:
    - EXACT same distance/angle as model1.png
    - IDENTICAL perspective, lighting, proportions
    
    CHARACTER:
    - Face matching input photo 100%
    - Professional {title} attire
    - Positioned in center like model1.png
    
    ENVIRONMENT:
    - Professional {title} workspace
    - Miniature furniture and tools
    - Text: {name}, {title}, {company}, {email}
    """
```

### Text Overlay Process:
```python
def _burn_text_into_image(self, image_path, card_info):
    # 1. Load image and create text lines
    text_lines = [name, title, company, email, phone]
    
    # 2. Create elegant overlay
    overlay = semi-transparent dark background (25,25,25,180)
    corner_radius = 20px
    
    # 3. Add text with hierarchy
    font_size = 32px (name), 24px (others)
    colors = white (name), light gray (title), gray (info)
    
    # 4. Resize to business card
    card_size = (1063, 650)  # 9x5.5cm at 300 DPI
    image.save(card_path, dpi=(300, 300))
```

## 🔧 KEY CONFIGURATION VALUES

### API Settings:
```python
# Gemini 2.0 Image Generation
model_name = "gemini-2.0-flash-preview-image-generation"
response_modalities = ["IMAGE"]

# Gemini 2.5 OCR
ocr_model = "gemini-2.5-flash"
```

### Image Processing:
```python
# Business Card Dimensions
CARD_WIDTH = 1063   # 9cm at 300 DPI
CARD_HEIGHT = 650   # 5.5cm at 300 DPI

# Text Overlay Settings
FONT_SIZE_NAME = 32
FONT_SIZE_INFO = 24
BACKGROUND_COLOR = (25, 25, 25, 180)
CORNER_RADIUS = 20
```

## 📊 WORKFLOW CONTROLLER

### Main Orchestration:
```python
class WorkflowController:
    def __init__(self):
        self.ocr_service = OCRService()
        self.ai_generator = AIImageGenerator()
        self.camera_manager = CameraManager()
    
    def process_card_upload(file):
        # 1. Save uploaded card image
        # 2. Extract text using OCR
        # 3. Return extracted info for verification
    
    def generate_ai_images(face_path, card_info):
        # 1. Generate 2 AI dollhouse variations
        # 2. Add text overlay to each
        # 3. Resize to business card format
        # 4. Save to outputs/ and sessions/
```

## 🎯 CURRENT PROMPT EXAMPLES

### Face Analysis Prompt:
```python
analysis_prompt = f"""
Analyze this person's face in EXTREME detail for toy figurine creation.
Focus on: hair, eyes, nose, mouth, face shape, skin tone, distinctive features.
Create 1500+ word description for PERFECT face matching.
Person: {name}, Occupation: {title}
"""
```

### Dollhouse Generation Prompt:
```python
dollhouse_prompt = f"""
CRITICAL: Replicate model1.png EXACTLY
- Camera: EXACT same distance/angle as model1.png
- Lighting: IDENTICAL to model1.png
- Character: {face_analysis} with {title} attire
- Environment: Professional {title} workspace
- Text: {name}, {title}, {company}, {email}
PERFECT MATCH to model1.png - no deviations allowed
"""
```

## 🚀 RUNNING & DEBUGGING

### Start Application:
```bash
python ai_card_visit_app.py
# Output: http://localhost:5000
```

### Key Debug Points:
1. **OCR Extraction**: Check extracted text accuracy
2. **Face Analysis**: Verify detailed face description
3. **AI Generation**: Monitor Gemini 2.0 response
4. **Text Overlay**: Check font sizes and positioning
5. **Final Resize**: Verify 9x5.5cm dimensions

### Common Issues & Solutions:
```python
# Issue: Gemini 2.0 response modalities error
# Solution: Use response_modalities=["IMAGE"]

# Issue: Text overlay too small
# Solution: Increased font sizes (32px, 24px)

# Issue: Business card too small
# Solution: 1063x650px at 300 DPI

# Issue: AI not matching model1.png
# Solution: Ultra-detailed prompts with EXACT replication
```

## 📁 SESSION MANAGEMENT

### File Organization:
```python
# Session structure:
sessions/{timestamp}/
├── card_image.jpg          # Original card
├── face_image.jpg          # Captured face
├── extracted_info.json     # OCR results
├── ai_image_1.png         # AI variation 1
├── ai_image_2.png         # AI variation 2
├── ai_image_1_card.png    # Final card 1
└── ai_image_2_card.png    # Final card 2

# Outputs structure:
outputs/
├── gemini20_dollhouse_{name}_{timestamp}_1.png
└── gemini20_dollhouse_{name}_{timestamp}_2.png
```

This code structure summary gives ChatGPT everything needed to understand and modify the codebase immediately.
