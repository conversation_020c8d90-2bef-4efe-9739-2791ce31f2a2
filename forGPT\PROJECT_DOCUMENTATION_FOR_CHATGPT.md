# AI Card Visit Project - Complete Documentation for ChatGPT

## 🎯 PROJECT OVERVIEW
**AI Card Visit System** - Tạo business card AI từ ảnh chân dung và thông tin card visit
- **Input**: Card visit image + Face photo (webcam/file)
- **Output**: AI-generated dollhouse business card (9x5.5cm) với text overlay

## 🏗️ ARCHITECTURE & FILES

### Core Files:
1. **`ai_card_visit_app.py`** - Flask web application (main entry point)
2. **`ai_generator.py`** - AI image generation using Gemini 2.0 Flash Preview
3. **`ocr_service.py`** - OCR extraction using Gemini 2.5 Flash
4. **`camera_manager.py`** - Camera/file input management
5. **`workflow_controller.py`** - Orchestrates entire workflow

### Key Directories:
- **`outputs/`** - Generated AI images
- **`sessions/`** - Session-based image storage
- **`templates/`** - HTML templates
- **`static/`** - CSS, JS, images

## 🔧 TECHNICAL STACK

### AI Services:
- **Gemini 2.0 Flash Preview Image Generation** - Main AI image generator
- **Gemini 2.5 Flash** - OCR text extraction
- **Google GenAI SDK** - API integration

### Web Framework:
- **Flask** - Python web framework
- **HTML/CSS/JavaScript** - Frontend
- **Bootstrap** - UI framework

### Key Dependencies:
```python
google-generativeai>=0.8.3
Pillow>=10.0.0
Flask>=2.3.0
opencv-python>=4.8.0
```

## 🎨 AI IMAGE GENERATION PROCESS

### Current Implementation:
1. **Face Analysis** - Ultra-detailed face analysis using Gemini 2.5
2. **Prompt Creation** - Generate dollhouse scene prompt matching `model1.png`
3. **Image Generation** - Gemini 2.0 creates 2 variations
4. **Text Overlay** - Add extracted card info with elegant design
5. **Business Card Resize** - Final output: 9x5.5cm (1063x650px at 300 DPI)

### Reference Image:
- **`model1.png`** - Target style for AI generation (dollhouse miniature scene)

## 📝 WORKFLOW STEPS

1. **Step 1**: Upload card visit image
2. **Step 2**: OCR extracts text (name, title, company, email, phone)
3. **Step 3**: User verifies/edits extracted information
4. **Step 4**: Capture face photo (webcam or file upload)
5. **Step 5**: AI generates dollhouse images (2 variations)
6. **Step 6**: Add text overlay and resize to business card format
7. **Step 7**: User selects and downloads final result

## 🎯 CURRENT AI GENERATION SETTINGS

### Prompt Strategy:
```python
# Target: Replicate model1.png EXACTLY
- Camera positioned at EXACT same distance/angle as model1.png
- IDENTICAL perspective, lighting, proportions
- Professional dollhouse miniature scene
- Character face matching input photo 100%
- Occupation-specific environment
```

### Text Overlay Design:
```python
# Business Card Format (9x5.5cm)
- Dark semi-transparent background (25,25,25,180)
- Corner radius: 20px
- Font sizes: 32px (name), 24px (other info)
- Text hierarchy: Name (white) > Title (light gray) > Info (gray)
- No icons, clean professional design
```

## 🔑 KEY CONFIGURATION

### Environment Variables:
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

### Model Settings:
```python
# Gemini 2.0 Flash Preview Image Generation
model_name = "gemini-2.0-flash-preview-image-generation"
response_modalities = ["IMAGE"]
```

## 🚀 RUNNING THE PROJECT

### Start Application:
```bash
python ai_card_visit_app.py
```

### Access:
- **URL**: http://localhost:5000
- **Mode**: Testing (File import + Webcam)

## 🎨 CURRENT CHALLENGES & SOLUTIONS

### 1. AI Image Quality:
- **Issue**: Generated images not matching model1.png quality
- **Solution**: Ultra-detailed prompts with EXACT replication requirements

### 2. Text Overlay Design:
- **Issue**: Previous design had icons and poor typography
- **Solution**: Clean, professional design with text hierarchy

### 3. Business Card Size:
- **Issue**: Output too small for printing
- **Solution**: 9x5.5cm format (1063x650px at 300 DPI)

## 📊 API USAGE PATTERNS

### Gemini 2.0 Image Generation:
```python
response = self.client.models.generate_content(
    model="gemini-2.0-flash-preview-image-generation",
    contents=[prompt, face_image]
)
```

### Gemini 2.5 OCR:
```python
response = self.client.models.generate_content(
    model="gemini-2.5-flash",
    contents=[ocr_prompt, card_image]
)
```

## 🎯 USER PREFERENCES & REQUIREMENTS

### Design Preferences:
- **Style**: Professional dollhouse miniature scenes (like model1.png)
- **Colors**: Light blue RGB(40, 205, 234) for UI
- **Text**: Clean, no icons, elegant typography
- **Size**: Standard business card 9x5.5cm

### Technical Requirements:
- **Speed**: AI generation under 1 minute
- **Quality**: 4K quality, print-ready
- **Face Matching**: 100% accuracy with input photo
- **Text Integration**: Extracted info overlaid on AI image

## 🔧 DEVELOPMENT NOTES

### Recent Updates:
1. **Prompt Optimization** - Updated to replicate model1.png exactly
2. **Text Overlay Redesign** - Removed icons, improved typography
3. **Business Card Sizing** - Proper 9x5.5cm format
4. **API Integration** - Fixed Gemini 2.0 response modalities

### Next Steps:
- Continue improving AI image quality to match model1.png
- Optimize generation speed
- Enhance face matching accuracy
- Refine text overlay design

## 📁 FILE STRUCTURE SUMMARY
```
AI_Image_And_Text/
├── ai_card_visit_app.py      # Main Flask app
├── ai_generator.py           # AI image generation
├── ocr_service.py           # OCR text extraction
├── camera_manager.py        # Camera/file management
├── workflow_controller.py   # Workflow orchestration
├── model1.png              # Reference image for AI generation
├── outputs/                # Generated images
├── sessions/              # Session storage
├── templates/             # HTML templates
└── static/               # CSS, JS, assets
```

This documentation provides everything ChatGPT needs to understand and continue working on the AI Card Visit project immediately.
