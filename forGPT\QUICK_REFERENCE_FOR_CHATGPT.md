# AI Card Visit - Quick Reference for ChatGPT

## 🚀 IMMEDIATE SETUP

### 1. Start Application:
```bash
cd "d:\THUC TAP\AI_Image_And_Text"
python ai_card_visit_app.py
# Access: http://localhost:5000
```

### 2. Environment:
```bash
# Required:
GEMINI_API_KEY=your_api_key_here

# Dependencies:
pip install google-generativeai>=0.8.3 Pillow Flask opencv-python
```

## 🎯 CURRENT STATUS & ISSUES

### ✅ Working Features:
- Flask web application running
- OCR text extraction (Gemini 2.5)
- Face capture (webcam + file upload)
- AI image generation (Gemini 2.0)
- Text overlay with elegant design
- Business card sizing (9x5.5cm)

### 🔧 Current Challenge:
**AI images not matching model1.png quality/style**
- Need to improve prompt engineering
- Target: Exact replication of model1.png dollhouse style
- Issue: Generated images don't match reference quality

## 📝 KEY FILES TO MODIFY

### 1. AI Generation (`ai_generator.py`):
```python
# Lines 133-184: Main prompt creation
# Lines 815-932: Detailed dollhouse prompt
# Lines 525-535: Business card resizing
# Lines 481-522: Text overlay design
```

### 2. Web Interface (`templates/index.html`):
```html
<!-- UI improvements -->
<!-- Color scheme: RGB(40, 205, 234) -->
```

### 3. OCR Service (`ocr_service.py`):
```python
# Lines 45-85: Text extraction logic
```

## 🎨 CURRENT AI SETTINGS

### Gemini 2.0 Configuration:
```python
model_name = "gemini-2.0-flash-preview-image-generation"
response_modalities = ["IMAGE"]
client = genai.Client(api_key=api_key)
```

### Prompt Strategy:
```python
# Target: Replicate model1.png EXACTLY
prompt_focus = [
    "EXACT camera positioning as model1.png",
    "IDENTICAL perspective and lighting",
    "Professional dollhouse miniature scene",
    "100% face matching with input photo",
    "Occupation-specific environment"
]
```

### Text Overlay Design:
```python
# Business Card Format
card_size = (1063, 650)  # 9x5.5cm at 300 DPI
background = (25, 25, 25, 180)  # Dark semi-transparent
font_sizes = {
    "name": 32,      # White, bold
    "title": 24,     # Light gray
    "info": 24       # Gray
}
corner_radius = 20
```

## 🔍 DEBUGGING CHECKLIST

### 1. Check AI Generation:
```python
# In ai_generator.py, line ~200
print(f"Generated images: {len(images)}")
print(f"Image paths: {image_paths}")
```

### 2. Verify OCR Extraction:
```python
# In ocr_service.py, line ~80
print(f"Extracted: {extracted_info}")
```

### 3. Monitor Text Overlay:
```python
# In ai_generator.py, line ~535
print(f"Business card created: {card_path}")
```

## 📊 USER PREFERENCES

### Design Requirements:
- **Style**: Professional dollhouse like model1.png
- **Size**: 9x5.5cm business card format
- **Quality**: 4K, print-ready (300 DPI)
- **Text**: Clean, no icons, elegant typography
- **Speed**: Under 1 minute generation time

### Technical Preferences:
- **AI Model**: Gemini 2.0 Flash Preview (current)
- **OCR**: Gemini 2.5 Flash (current)
- **Face Input**: Webcam + file upload
- **Output**: 2 variations for selection

## 🎯 IMPROVEMENT PRIORITIES

### 1. AI Image Quality (HIGH PRIORITY):
```python
# Focus areas:
- Prompt engineering to match model1.png exactly
- Camera angle and perspective matching
- Lighting and atmosphere replication
- Character positioning and scale
```

### 2. Face Matching Accuracy:
```python
# Current approach:
- Ultra-detailed face analysis (1500+ words)
- Specific facial feature descriptions
- Integration with dollhouse scene
```

### 3. Text Integration:
```python
# Current design:
- Semi-transparent overlay
- Professional typography hierarchy
- Business card proportions
```

## 🔧 COMMON MODIFICATIONS

### Update AI Prompt:
```python
# File: ai_generator.py
# Function: _create_dollhouse_prompt()
# Lines: 815-932
```

### Change Text Overlay:
```python
# File: ai_generator.py
# Function: _burn_text_into_image()
# Lines: 430-535
```

### Modify Business Card Size:
```python
# File: ai_generator.py
# Line: 527
card_size = (width, height)  # Adjust dimensions
```

### Update UI Colors:
```python
# File: templates/index.html
# Current: RGB(40, 205, 234) light blue
```

## 📁 IMPORTANT PATHS

### Reference Files:
- **`model1.png`** - Target style for AI generation
- **`face1.jpg`** - Example face input
- **`lambuoi1.png`** - Example output

### Generated Files:
- **`outputs/`** - Final AI images
- **`sessions/{timestamp}/`** - Session data
- **`static/uploads/`** - Temporary uploads

## 🚨 CRITICAL NOTES

### 1. API Limits:
- Gemini 2.0 has usage quotas
- Monitor API calls and costs

### 2. Image Quality:
- Current challenge: AI not matching model1.png
- Need continuous prompt refinement

### 3. Performance:
- Target: <1 minute generation
- Current: Variable based on Gemini response

### 4. File Management:
- Clean up sessions periodically
- Monitor disk space usage

This quick reference provides ChatGPT with immediate actionable information to continue development efficiently.
