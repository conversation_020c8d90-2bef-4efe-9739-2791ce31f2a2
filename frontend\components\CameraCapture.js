import { useState, useRef, useCallback } from 'react'
import Webcam from 'react-webcam'

export default function CameraCapture({ onCapture }) {
  const [capturing, setCapturing] = useState(false)
  const [capturedImage, setCapturedImage] = useState(null)
  const webcamRef = useRef(null)

  const capture = useCallback(async () => {
    const imageSrc = webcamRef.current.getScreenshot()
    if (imageSrc) {
      setCapturing(true)
      try {
        // Convert base64 to blob
        const response = await fetch(imageSrc)
        const blob = await response.blob()
        
        // Create form data
        const formData = new FormData()
        formData.append('file', blob, 'face_capture.jpg')
        
        // Upload to server
        const uploadResponse = await fetch('/api/upload-face', {
          method: 'POST',
          body: formData,
        })
        
        const result = await uploadResponse.json()
        
        if (result.success) {
          setCapturedImage(imageSrc)
          onCapture(result.file_path)
        } else {
          alert('Lỗi khi lưu ảnh: ' + result.error)
        }
      } catch (error) {
        alert('Lỗi khi chụp ảnh: ' + error.message)
      } finally {
        setCapturing(false)
      }
    }
  }, [webcamRef, onCapture])

  const retake = () => {
    setCapturedImage(null)
  }

  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: "user"
  }

  if (capturedImage) {
    return (
      <div className="card max-w-2xl mx-auto text-center">
        <h3 className="text-xl font-bold mb-4">Ảnh đã chụp</h3>
        <div className="mb-4">
          <img 
            src={capturedImage} 
            alt="Captured face" 
            className="mx-auto rounded-lg shadow-md max-w-md"
          />
        </div>
        <div className="space-x-4">
          <button
            onClick={retake}
            className="btn-secondary"
          >
            Chụp lại
          </button>
          <button
            onClick={() => onCapture(capturedImage)}
            className="btn-primary"
          >
            Sử dụng ảnh này
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="card max-w-2xl mx-auto text-center">
      <h3 className="text-xl font-bold mb-4">Chụp ảnh khuôn mặt</h3>
      <p className="text-gray-600 mb-6">
        Hãy ngồi thẳng và nhìn vào camera. Đảm bảo khuôn mặt được chiếu sáng đều.
      </p>
      
      <div className="mb-6">
        <Webcam
          audio={false}
          ref={webcamRef}
          screenshotFormat="image/jpeg"
          videoConstraints={videoConstraints}
          className="mx-auto rounded-lg shadow-md"
        />
      </div>
      
      <button
        onClick={capture}
        disabled={capturing}
        className={`btn-primary ${capturing ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {capturing ? 'Đang chụp...' : 'Chụp ảnh'}
      </button>
      
      <div className="mt-4 text-sm text-gray-500">
        <p>💡 Mẹo: Đảm bảo ánh sáng tốt và khuôn mặt rõ nét</p>
      </div>
    </div>
  )
}
