import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'

export default function CardUpload({ onUpload }) {
  const [uploading, setUploading] = useState(false)
  const [uploadedImage, setUploadedImage] = useState(null)
  const [cardInfo, setCardInfo] = useState(null)

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0]
    if (file) {
      setUploading(true)
      try {
        // Create form data
        const formData = new FormData()
        formData.append('file', file)
        
        // Upload file
        const uploadResponse = await fetch('/api/upload-card', {
          method: 'POST',
          body: formData,
        })
        
        const uploadResult = await uploadResponse.json()
        
        if (uploadResult.success) {
          // Extract card information
          const extractResponse = await fetch('/api/extract-card-info', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              file_path: uploadResult.file_path
            }),
          })
          
          const extractResult = await extractResponse.json()
          
          if (extractResult.success) {
            setUploadedImage(URL.createObjectURL(file))
            setCardInfo(extractResult.card_info)
            onUpload(uploadResult.file_path)
          } else {
            alert('Lỗi khi trích xuất thông tin: ' + extractResult.error)
          }
        } else {
          alert('Lỗi khi upload: ' + uploadResult.error)
        }
      } catch (error) {
        alert('Lỗi khi xử lý file: ' + error.message)
      } finally {
        setUploading(false)
      }
    }
  }, [onUpload])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    multiple: false
  })

  const resetUpload = () => {
    setUploadedImage(null)
    setCardInfo(null)
  }

  if (uploadedImage && cardInfo) {
    return (
      <div className="card max-w-4xl mx-auto">
        <h3 className="text-xl font-bold mb-4 text-center">Card visit đã upload</h3>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* Uploaded Image */}
          <div>
            <h4 className="font-medium mb-2">Ảnh card visit:</h4>
            <img 
              src={uploadedImage} 
              alt="Uploaded card" 
              className="w-full rounded-lg shadow-md"
            />
          </div>
          
          {/* Extracted Information */}
          <div>
            <h4 className="font-medium mb-2">Thông tin trích xuất:</h4>
            <div className="space-y-3 text-sm">
              {cardInfo.name && (
                <div>
                  <span className="font-medium text-gray-700">Tên:</span>
                  <span className="ml-2">{cardInfo.name}</span>
                </div>
              )}
              {cardInfo.position && (
                <div>
                  <span className="font-medium text-gray-700">Chức vụ:</span>
                  <span className="ml-2">{cardInfo.position}</span>
                </div>
              )}
              {cardInfo.company && (
                <div>
                  <span className="font-medium text-gray-700">Công ty:</span>
                  <span className="ml-2">{cardInfo.company}</span>
                </div>
              )}
              {cardInfo.email && (
                <div>
                  <span className="font-medium text-gray-700">Email:</span>
                  <span className="ml-2">{cardInfo.email}</span>
                </div>
              )}
              {cardInfo.phone && (
                <div>
                  <span className="font-medium text-gray-700">Điện thoại:</span>
                  <span className="ml-2">{cardInfo.phone}</span>
                </div>
              )}
              {cardInfo.website && (
                <div>
                  <span className="font-medium text-gray-700">Website:</span>
                  <span className="ml-2">{cardInfo.website}</span>
                </div>
              )}
              {cardInfo.address && (
                <div>
                  <span className="font-medium text-gray-700">Địa chỉ:</span>
                  <span className="ml-2">{cardInfo.address}</span>
                </div>
              )}
            </div>
            
            {/* Raw text for debugging */}
            {cardInfo.raw_text && cardInfo.raw_text.length > 0 && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Xem text gốc đã trích xuất
                </summary>
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                  {cardInfo.raw_text.map((text, index) => (
                    <div key={index}>{text}</div>
                  ))}
                </div>
              </details>
            )}
          </div>
        </div>
        
        <div className="text-center mt-6">
          <button
            onClick={resetUpload}
            className="btn-secondary"
          >
            Upload card khác
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="card max-w-2xl mx-auto text-center">
      <h3 className="text-xl font-bold mb-4">Upload card visit cũ</h3>
      <p className="text-gray-600 mb-6">
        Kéo thả hoặc click để chọn ảnh card visit cần trích xuất thông tin
      </p>
      
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 cursor-pointer transition-colors ${
          isDragActive 
            ? 'border-primary-500 bg-primary-50' 
            : 'border-gray-300 hover:border-primary-400'
        } ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} disabled={uploading} />
        
        <div className="text-center">
          <svg
            className="mx-auto h-12 w-12 text-gray-400 mb-4"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          
          {uploading ? (
            <p className="text-gray-600">Đang xử lý...</p>
          ) : isDragActive ? (
            <p className="text-primary-600">Thả file vào đây...</p>
          ) : (
            <div>
              <p className="text-gray-600 mb-2">
                Kéo thả ảnh vào đây, hoặc <span className="text-primary-600">click để chọn</span>
              </p>
              <p className="text-sm text-gray-500">
                Hỗ trợ: JPG, PNG, GIF (tối đa 10MB)
              </p>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-4 text-sm text-gray-500">
        <p>💡 Mẹo: Chụp card visit với ánh sáng tốt và text rõ nét để OCR chính xác hơn</p>
      </div>
    </div>
  )
}
