import { useState, useEffect } from 'react'

export default function ProcessingStatus() {
  const [currentStep, setCurrentStep] = useState(0)
  
  const steps = [
    '<PERSON><PERSON> tr<PERSON><PERSON> xuất thông tin từ card visit...',
    '<PERSON><PERSON> t<PERSON><PERSON>nh AI từ khuôn mặt...',
    '<PERSON><PERSON> thiết kế card visit mới...',
    '<PERSON>àn thành!'
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < steps.length - 1) {
          return prev + 1
        }
        return 0 // Reset to create loop effect
      })
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="text-center">
      <div className="mb-6">
        {/* Spinning loader */}
        <div className="inline-flex items-center justify-center w-16 h-16 border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin mb-4"></div>
        
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          <PERSON><PERSON> xử lý...
        </h3>
        
        <p className="text-gray-600">
          {steps[currentStep]}
        </p>
      </div>
      
      {/* Progress steps */}
      <div className="space-y-2">
        {steps.slice(0, -1).map((step, index) => (
          <div
            key={index}
            className={`flex items-center text-sm ${
              index <= currentStep ? 'text-primary-600' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-4 h-4 rounded-full mr-3 flex-shrink-0 ${
                index < currentStep
                  ? 'bg-green-500'
                  : index === currentStep
                  ? 'bg-primary-500 animate-pulse'
                  : 'bg-gray-300'
              }`}
            >
              {index < currentStep && (
                <svg
                  className="w-4 h-4 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span>{step}</span>
          </div>
        ))}
      </div>
      
      <div className="mt-6 text-sm text-gray-500">
        <p>Quá trình này có thể mất 30-60 giây...</p>
      </div>
    </div>
  )
}
