import { useState } from 'react'

export default function ResultDisplay({ result }) {
  const [selectedView, setSelectedView] = useState('final')

  const downloadImage = (imagePath, filename) => {
    const link = document.createElement('a')
    link.href = `http://localhost:8000/${imagePath}`
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="card max-w-6xl mx-auto">
      <h3 className="text-2xl font-bold text-center mb-6">
        🎉 Card visit mới đã được tạo thành công!
      </h3>
      
      {/* View Selection */}
      <div className="flex justify-center mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          <button
            onClick={() => setSelectedView('final')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedView === 'final'
                ? 'bg-primary-500 text-white'
                : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            Card hoàn thành
          </button>
          <button
            onClick={() => setSelectedView('ai_face')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedView === 'ai_face'
                ? 'bg-primary-500 text-white'
                : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            Ảnh AI
          </button>
          <button
            onClick={() => setSelectedView('info')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedView === 'info'
                ? 'bg-primary-500 text-white'
                : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            Thông tin
          </button>
        </div>
      </div>

      {/* Content Display */}
      {selectedView === 'final' && (
        <div className="text-center">
          <h4 className="text-lg font-medium mb-4">Card visit hoàn thành</h4>
          <div className="mb-6">
            <img
              src={`http://localhost:8000/${result.final_card_path}`}
              alt="Final card"
              className="mx-auto max-w-full h-auto rounded-lg shadow-lg"
              style={{ maxHeight: '400px' }}
            />
          </div>
          <button
            onClick={() => downloadImage(result.final_card_path, 'card_visit_moi.png')}
            className="btn-primary"
          >
            📥 Tải xuống card visit
          </button>
        </div>
      )}

      {selectedView === 'ai_face' && (
        <div className="text-center">
          <h4 className="text-lg font-medium mb-4">Ảnh AI đã tạo</h4>
          <div className="mb-6">
            <img
              src={`http://localhost:8000/${result.ai_face_path}`}
              alt="AI generated face"
              className="mx-auto max-w-sm h-auto rounded-lg shadow-lg"
            />
          </div>
          <button
            onClick={() => downloadImage(result.ai_face_path, 'anh_ai.png')}
            className="btn-secondary"
          >
            📥 Tải xuống ảnh AI
          </button>
        </div>
      )}

      {selectedView === 'info' && (
        <div>
          <h4 className="text-lg font-medium mb-4 text-center">Thông tin đã trích xuất</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              {result.card_info.name && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">Tên:</span>
                  <span>{result.card_info.name}</span>
                </div>
              )}
              {result.card_info.position && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">Chức vụ:</span>
                  <span>{result.card_info.position}</span>
                </div>
              )}
              {result.card_info.company && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">Công ty:</span>
                  <span>{result.card_info.company}</span>
                </div>
              )}
            </div>
            <div className="space-y-3">
              {result.card_info.email && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">Email:</span>
                  <span className="break-all">{result.card_info.email}</span>
                </div>
              )}
              {result.card_info.phone && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">SĐT:</span>
                  <span>{result.card_info.phone}</span>
                </div>
              )}
              {result.card_info.website && (
                <div className="flex">
                  <span className="font-medium text-gray-700 w-24">Website:</span>
                  <span className="break-all">{result.card_info.website}</span>
                </div>
              )}
            </div>
          </div>
          {result.card_info.address && (
            <div className="mt-4">
              <span className="font-medium text-gray-700">Địa chỉ:</span>
              <p className="mt-1">{result.card_info.address}</p>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4 mt-8 pt-6 border-t border-gray-200">
        <button
          onClick={() => downloadImage(result.final_card_path, 'card_visit_moi.png')}
          className="btn-primary"
        >
          📥 Tải xuống card
        </button>
        <button
          onClick={() => window.print()}
          className="btn-secondary"
        >
          🖨️ In card
        </button>
        <button
          onClick={() => {
            if (navigator.share) {
              navigator.share({
                title: 'Card visit mới',
                text: 'Xem card visit mới được tạo bằng AI',
                url: window.location.href
              })
            } else {
              // Fallback: copy link to clipboard
              navigator.clipboard.writeText(window.location.href)
              alert('Link đã được copy vào clipboard!')
            }
          }}
          className="btn-secondary"
        >
          📤 Chia sẻ
        </button>
      </div>

      {/* Success Message */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center">
          <svg
            className="w-5 h-5 text-green-500 mr-2"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
          <span className="text-green-800 font-medium">
            Card visit đã được tạo thành công với ảnh AI phong cách anime!
          </span>
        </div>
      </div>
    </div>
  )
}
