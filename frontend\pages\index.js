import { useState } from 'react'
import Head from 'next/head'
import Camera<PERSON><PERSON><PERSON> from '../components/CameraCapture'
import CardUpload from '../components/CardUpload'
import ProcessingStatus from '../components/ProcessingStatus'
import ResultDisplay from '../components/ResultDisplay'

export default function Home() {
  const [step, setStep] = useState(1)
  const [capturedFace, setCapturedFace] = useState(null)
  const [uploadedCard, setUploadedCard] = useState(null)
  const [processing, setProcessing] = useState(false)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)

  const handleFaceCapture = (imagePath) => {
    setCapturedFace(imagePath)
    setStep(2)
  }

  const handleCardUpload = (imagePath) => {
    setUploadedCard(imagePath)
    setStep(3)
  }

  const handleProcess = async () => {
    if (!capturedFace || !uploadedCard) {
      setError('Vui lòng hoàn thành cả hai bước trước')
      return
    }

    setProcessing(true)
    setError(null)

    try {
      const response = await fetch('/api/process-complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          face_image_path: capturedFace,
          card_image_path: uploadedCard,
          style: 'anime',
          template: 'modern'
        }),
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
        setStep(4)
      } else {
        setError(data.error || 'Có lỗi xảy ra trong quá trình xử lý')
      }
    } catch (err) {
      setError('Không thể kết nối đến server')
    } finally {
      setProcessing(false)
    }
  }

  const resetProcess = () => {
    setStep(1)
    setCapturedFace(null)
    setUploadedCard(null)
    setProcessing(false)
    setResult(null)
    setError(null)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>AI Card Visit Generator</title>
        <meta name="description" content="Tạo card visit với AI" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI Card Visit Generator
          </h1>
          <p className="text-xl text-gray-600">
            Tạo card visit mới với ảnh AI từ ảnh thật của bạn
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3, 4].map((stepNum) => (
              <div key={stepNum} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                    step >= stepNum ? 'bg-primary-500' : 'bg-gray-300'
                  }`}
                >
                  {stepNum}
                </div>
                {stepNum < 4 && (
                  <div
                    className={`w-16 h-1 ${
                      step > stepNum ? 'bg-primary-500' : 'bg-gray-300'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Labels */}
        <div className="flex justify-center mb-8">
          <div className="grid grid-cols-4 gap-8 text-center text-sm">
            <div className={step >= 1 ? 'text-primary-600 font-medium' : 'text-gray-500'}>
              Chụp ảnh mặt
            </div>
            <div className={step >= 2 ? 'text-primary-600 font-medium' : 'text-gray-500'}>
              Upload card cũ
            </div>
            <div className={step >= 3 ? 'text-primary-600 font-medium' : 'text-gray-500'}>
              Xử lý AI
            </div>
            <div className={step >= 4 ? 'text-primary-600 font-medium' : 'text-gray-500'}>
              Kết quả
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-2xl mx-auto mb-6">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          </div>
        )}

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {step === 1 && (
            <CameraCapture onCapture={handleFaceCapture} />
          )}

          {step === 2 && (
            <div>
              <CardUpload onUpload={handleCardUpload} />
              {capturedFace && (
                <div className="mt-6 text-center">
                  <p className="text-green-600 mb-2">✓ Đã chụp ảnh mặt thành công</p>
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary"
                  >
                    Chụp lại ảnh mặt
                  </button>
                </div>
              )}
            </div>
          )}

          {step === 3 && (
            <div className="text-center">
              <div className="card max-w-md mx-auto">
                <h3 className="text-xl font-bold mb-4">Sẵn sàng xử lý</h3>
                <div className="space-y-3 mb-6">
                  <p className="text-green-600">✓ Đã chụp ảnh mặt</p>
                  <p className="text-green-600">✓ Đã upload card visit</p>
                </div>
                
                {processing ? (
                  <ProcessingStatus />
                ) : (
                  <div className="space-y-4">
                    <button
                      onClick={handleProcess}
                      className="btn-primary w-full"
                    >
                      Bắt đầu tạo card mới
                    </button>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setStep(1)}
                        className="btn-secondary flex-1"
                      >
                        Chụp lại ảnh
                      </button>
                      <button
                        onClick={() => setStep(2)}
                        className="btn-secondary flex-1"
                      >
                        Upload lại card
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {step === 4 && result && (
            <div>
              <ResultDisplay result={result} />
              <div className="text-center mt-6">
                <button
                  onClick={resetProcess}
                  className="btn-primary"
                >
                  Tạo card mới
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
