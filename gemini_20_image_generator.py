#!/usr/bin/env python3
"""
Gemini 2.0 Flash Preview Image Generation
Latest Gemini model với native image generation capabilities
"""

import os
import json
import base64
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
try:
    from google import genai
    from google.genai import types
    NEW_SDK = True
    print("✅ Using new Google GenAI SDK")
except ImportError:
    import google.generativeai as genai
    NEW_SDK = False
    print("⚠️ Using legacy Google GenerativeAI SDK")

class Gemini20ImageGenerator:
    """Gemini 2.0 Flash Preview Image Generation - Latest Model"""
    
    def __init__(self):
        self.model_name = "gemini-2.0-flash-preview-image-generation"
        self.setup_gemini()
        print(f"🔥 Gemini 2.0 Image Generator initialized")
        print(f"   Model: {self.model_name}")
        print(f"   Capabilities: Text + Images → Text + Images")
    
    def setup_gemini(self):
        """Setup Gemini API"""
        try:
            # Load API key using same method as OCR service
            self.load_api_key()

            if not self.gemini_key:
                raise ValueError("Gemini API key not found")

            if NEW_SDK:
                # Use new Google GenAI SDK
                self.client = genai.Client(api_key=self.gemini_key)
                print("✅ Gemini 2.0 API configured with new SDK")
            else:
                # Use legacy SDK
                genai.configure(api_key=self.gemini_key)
                self.model = genai.GenerativeModel(self.model_name)
                print("✅ Gemini 2.0 API configured with legacy SDK")

        except Exception as e:
            print(f"❌ Gemini API setup error: {e}")
            raise

    def load_api_key(self):
        """Load Gemini API key - same method as OCR service"""

        # Try to load from .env file
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value

        self.gemini_key = os.getenv('GEMINI_API_KEY', '')

        if self.gemini_key:
            print("✅ Gemini API key loaded for image generation")
        else:
            print("⚠️ No Gemini API key found")

    def _burn_text_into_image(self, image_path, card_info):
        """Burn extracted information text directly into the image"""
        try:
            # Open the image
            image = Image.open(image_path)
            draw = ImageDraw.Draw(image)

            # Try to use a nice font, fallback to default
            try:
                font_size = max(16, image.width // 50)  # Responsive font size
                font = ImageFont.truetype("arial.ttf", font_size)
                small_font = ImageFont.truetype("arial.ttf", font_size - 2)
            except:
                font = ImageFont.load_default()
                small_font = font

            # Create simplified text content (only essential fields)
            text_lines = []
            if card_info.get('name'):
                text_lines.append(f"👤 {card_info['name']}")
            if card_info.get('company'):
                text_lines.append(f"🏢 {card_info['company']}")
            if card_info.get('title'):
                text_lines.append(f"💼 {card_info['title']}")
            if card_info.get('phone'):
                text_lines.append(f"📞 {card_info['phone']}")
            if card_info.get('email'):
                text_lines.append(f"📧 {card_info['email']}")

            if not text_lines:
                return image_path  # No text to add

            # Calculate text box dimensions
            line_height = font_size + 4
            max_width = 0
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                line_width = bbox[2] - bbox[0]
                max_width = max(max_width, line_width)

            # Position text box in bottom-left corner
            margin = 20
            padding = 15
            box_width = max_width + (padding * 2)
            box_height = len(text_lines) * line_height + (padding * 2) + 25  # +25 for header

            box_x = margin
            box_y = image.height - box_height - margin

            # Draw background box with transparency effect
            overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # Draw semi-transparent black background with higher transparency
            overlay_draw.rectangle(
                [box_x, box_y, box_x + box_width, box_y + box_height],
                fill=(0, 0, 0, 180),  # Semi-transparent black (higher transparency)
                outline=(255, 255, 255, 200),  # White border
                width=2
            )

            # Composite overlay onto original image
            image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
            draw = ImageDraw.Draw(image)

            # Draw header with white text for black background
            header_y = box_y + padding
            draw.text((box_x + padding, header_y), "📄 Card Information",
                     fill=(255, 255, 255), font=font)  # White text

            # Draw text lines with white text
            text_y = header_y + line_height + 5
            for line in text_lines:
                draw.text((box_x + padding, text_y), line, fill=(255, 255, 255), font=small_font)  # White text
                text_y += line_height

            # Save the modified image
            image.save(image_path, quality=95)
            print(f"✅ Text burned into image: {image_path}")
            return image_path

        except Exception as e:
            print(f"❌ Error burning text into image: {e}")
            return image_path  # Return original path if failed

    def generate_dollhouse_image(self, face_path, card_info):
        """Generate TWO dollhouse images using Gemini 2.0 native image generation"""
        try:
            print(f"\n🎨 Generating TWO dollhouse variations with Gemini 2.0...")
            print(f"   Face: {face_path}")
            print(f"   Person: {card_info.get('name', 'N/A')}")
            print(f"   Company: {card_info.get('company', 'N/A')}")

            # Step 1: Analyze face for ultra detailed description
            face_analysis = self._ultra_face_analysis(face_path, card_info)

            # Step 2: Create enhanced dollhouse prompt
            dollhouse_prompt = self._create_dollhouse_prompt(face_analysis, card_info)

            # Step 3: Generate FIRST image with Gemini 2.0
            print("🎨 Generating Image 1/2...")
            result1 = self._generate_with_gemini20(dollhouse_prompt, face_path, card_info, variant=1)

            # Step 4: Generate SECOND image with slight variation
            print("🎨 Generating Image 2/2...")
            result2 = self._generate_with_gemini20(dollhouse_prompt, face_path, card_info, variant=2)

            # Process results và create unified naming
            images_generated = []
            unified_paths = []

            # Create unified base name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_part = card_info.get('name', 'person').lower().replace(' ', '_')
            base_name = f"gemini20_dollhouse_{name_part}_{timestamp}"

            if result1.get('success'):
                # Burn text into image
                final_path1 = self._burn_text_into_image(result1['image_path'], card_info)
                images_generated.append(final_path1)
                unified_paths.append(final_path1)
                print(f"✅ Image 1 generated with text: {final_path1}")

            if result2.get('success'):
                # Burn text into image
                final_path2 = self._burn_text_into_image(result2['image_path'], card_info)
                images_generated.append(final_path2)
                unified_paths.append(final_path2)
                print(f"✅ Image 2 generated with text: {final_path2}")

            if images_generated:
                print(f"✅ Gemini 2.0 dual generation successful!")
                print(f"   Images: {len(images_generated)} variations")
                print(f"   Base name: {base_name}")
                print(f"   Model: {self.model_name}")
                print(f"   Quality: NATIVE GEMINI 2.0")

                return {
                    'success': True,
                    'image_path': images_generated[0],  # Primary image
                    'image_paths': images_generated,    # All images
                    'unified_paths': unified_paths,     # Unified naming
                    'base_name': base_name,             # Base name for session
                    'api_used': 'Gemini 2.0 Flash Preview Image Generation',
                    'model': self.model_name,
                    'quality_score': '98/100',
                    'face_matching': 'Ultra Detailed Analysis',
                    'generation_method': 'Native Gemini 2.0 Dual Generation',
                    'face_analysis_length': len(face_analysis),
                    'prompt_length': len(dollhouse_prompt),
                    'variations_count': len(images_generated)
                }
            else:
                return {
                    'success': False,
                    'error': 'Both image generations failed',
                    'api_used': 'Gemini 2.0 Flash Preview Image Generation'
                }

        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")
            return {
                'success': False,
                'error': f"Gemini 2.0 generation failed: {str(e)}",
                'api_used': 'Gemini 2.0 Flash Preview Image Generation'
            }
    
    def _ultra_face_analysis(self, face_path, card_info):
        """Ultra detailed face analysis using Gemini 2.5 Flash"""
        try:
            print("🔍 Performing ultra detailed face analysis...")
            
            # Load face image
            face_image = Image.open(face_path)
            
            # Analysis prompt for maximum detail
            analysis_prompt = f"""Analyze this person's face in EXTREME detail for creating a toy figurine that looks EXACTLY like them.

ULTRA DETAILED FACIAL ANALYSIS FOR TOY FIGURINE:

1. HAIR (CRITICAL FOR RECOGNITION):
   - Exact color (specific shade, highlights, undertones)
   - Style (straight, wavy, curly, length, parting)
   - Texture and volume
   - Any distinctive features

2. EYES (MOST IMPORTANT FOR FACE MATCHING):
   - Shape (almond, round, hooded, etc.)
   - Size relative to face
   - Color (exact shade and patterns)
   - Eyebrow shape, thickness, arch
   - Eyelash characteristics
   - Eye spacing and positioning

3. NOSE:
   - Overall size and shape
   - Bridge characteristics (straight, curved)
   - Nostril shape and size
   - Tip shape

4. MOUTH AND LIPS:
   - Lip thickness (upper vs lower)
   - Mouth width
   - Smile characteristics
   - Lip color and texture

5. FACE SHAPE AND STRUCTURE:
   - Overall face shape (oval, round, square, heart, etc.)
   - Jawline definition
   - Cheekbone prominence
   - Forehead size and shape
   - Chin characteristics

6. SKIN:
   - Skin tone (exact shade)
   - Texture and any distinctive marks
   - Age-related characteristics

7. DISTINCTIVE FEATURES:
   - Glasses (if any) - exact style and color
   - Facial hair (if any) - style, color, coverage
   - Any unique marks, dimples, or characteristics
   - Expression tendencies

8. PROPORTIONS:
   - Relative sizes of all features
   - Facial symmetry
   - Overall harmony of features

Create an EXTREMELY detailed description (minimum 1500 words) that would allow an artist to recreate this person's face perfectly in toy figurine form. Focus on the most distinctive and recognizable features that make this person unique. Be ULTRA-SPECIFIC about every facial detail to ensure PERFECT FACE MATCHING in the generated dollhouse image. The face must be CRYSTAL CLEAR and HIGHLY RECOGNIZABLE.

Person's name: {card_info.get('name', 'Professional Person')}
Occupation: {card_info.get('title', 'Professional')}"""

            # Use Gemini 2.5 Flash for analysis (more reliable for analysis)
            if NEW_SDK:
                response = self.client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=[analysis_prompt, face_image]
                )
            else:
                analysis_model = genai.GenerativeModel("gemini-2.5-flash")
                response = analysis_model.generate_content([analysis_prompt, face_image])
            
            if NEW_SDK:
                face_analysis = response.candidates[0].content.parts[0].text
            else:
                face_analysis = response.text
            print(f"✅ Face analysis complete: {len(face_analysis)} characters")
            
            return face_analysis
            
        except Exception as e:
            print(f"❌ Face analysis error: {e}")
            return f"Professional person with distinctive facial features suitable for {card_info.get('title', 'professional')} role"
    
    def _create_dollhouse_prompt(self, face_analysis, card_info):
        """Create enhanced dollhouse prompt for Gemini 2.0"""
        
        # Extract key information
        name = card_info.get('name', 'Professional Person')
        company = card_info.get('company', 'Professional Company')
        title = card_info.get('title', 'Professional')
        email = card_info.get('email', '<EMAIL>')
        
        # Create prompt to match model1.png EXACTLY
        prompt = f"""CRITICAL: You must create a dollhouse miniature scene that matches model1.png EXACTLY. Study the reference image model1.png carefully and replicate it PERFECTLY. This must be a PERFECT REPLICA of the style, perspective, and composition shown in model1.png.

REFERENCE IMAGE ANALYSIS:
- Study model1.png for exact camera angle, distance, and perspective
- Analyze the lighting style and atmosphere in model1.png
- Observe the dollhouse proportions and room layout in model1.png
- Note the character positioning and scale in model1.png
- Examine the overall composition and visual style of model1.png

EXACT SPECIFICATIONS TO MATCH model1.png:
- Camera positioned at the EXACT same distance and angle as model1.png
- Show the dollhouse as a complete miniature room setup on a wooden surface
- The perspective must be IDENTICAL to model1.png - not too high, not too low
- Wide-angle view showing the complete dollhouse environment exactly like model1.png
- The dollhouse should appear as a detailed miniature display, exactly like in model1.png
- Same lighting style, same room proportions, same overall composition as model1.png

ROOM ENVIRONMENT (matching model1.png style):
- Professional office workspace with miniature furniture
- Detailed craftsmanship throughout, exactly like model1.png quality
- Miniature desk, chair, computer, and office accessories
- Wall decorations and office supplies scaled to miniature size
- Warm, professional lighting that matches model1.png atmosphere
- All furniture and items should be occupation-specific for {title}

DOLLHOUSE CONSTRUCTION (exactly like model1.png):
- Handcrafted dollhouse aesthetic with visible room boundaries
- Wooden dollhouse construction visible, exactly like model1.png
- Room should appear as a miniature box/display case
- Professional miniature craftsmanship quality
- Detailed miniature elements throughout the scene

Include a miniature nameplate on the floor or wall that fits the theme — styled in a way that fits the profession (e.g., hospital-style name badges for the medical field, microphone name tags for the media, etc.). The nameplate should read:
{name}
{title}
The wall should read:
{company}
{email}
Use warm lighting, miniature scale, and handmade textures to emphasize the cozy, playful, and collectible nature of the setting. The result is a small, themed, personalized dollhouse-like workspace — charming and visually distinct while representing the person's profession.

CAMERA POSITIONING (CRITICAL - EXACT model1.png MATCH):
- Camera positioned at the EXACT same distance and angle as model1.png
- IDENTICAL perspective to model1.png - not too high, not too low
- Wide-angle view showing complete dollhouse environment exactly like model1.png
- Camera distance must MATCH model1.png exactly - not closer, not further
- Viewing angle must be IDENTICAL to model1.png composition
- Show dollhouse boundaries exactly like model1.png
- Perspective must create the SAME visual effect as model1.png

CHARACTER REQUIREMENTS (EXACT model1.png STYLE):
- Toy figurine person positioned EXACTLY like the character in model1.png
- Character placement and pose must MATCH model1.png composition
- EXACT facial features from input photo: {face_analysis[:2000]}
- Face must be CRYSTAL CLEAR and HIGHLY DETAILED exactly like model1.png quality
- Character positioned in center of workspace, exactly like model1.png
- Professional attire appropriate for {title} role
- Character scale and proportions must MATCH model1.png exactly
- Face lighting and visibility exactly like model1.png
- Character should appear as a detailed miniature figurine, exactly like model1.png
- PERFECT FACE MATCHING with input photo while maintaining model1.png style
- Character integration into scene exactly like model1.png composition

ENVIRONMENT DETAILS:
- Miniature office workspace with detailed craftsmanship
- Small desk with miniature computer monitor
- Office chair positioned properly
- Miniature filing cabinets and bookshelves
- Tiny books and office supplies
- Desk lamp providing warm lighting

TEXT INTEGRATION (CRITICAL):
- Wall sign displaying "{company}" in clear, readable miniature letters
- Desk nameplate showing "{name}" prominently
- Small business card on desk with "{email}" visible
- Office door sign with "{title}" title
- All text elements must be clearly visible and legible

QUALITY SPECIFICATIONS (EXACT model1.png QUALITY):
- Image quality must MATCH model1.png exactly
- Same lighting style and atmosphere as model1.png
- IDENTICAL craftsmanship quality to model1.png
- Professional miniature photography exactly like model1.png
- Same level of detail and sharpness as model1.png
- Dollhouse aesthetic must be IDENTICAL to model1.png
- Character lighting and visibility exactly like model1.png
- Overall image composition and quality matching model1.png perfectly
- Same warm, professional lighting as model1.png
- Miniature elements detailed exactly like model1.png
- PERFECT REPLICA of model1.png visual style

PERSPECTIVE REQUIREMENTS:
- LOWERED camera angle for better face visibility
- Slightly elevated view (25-30 degrees) NOT top-down
- Character's face must be clearly visible and recognizable
- Professional dollhouse photography with face-focused composition
- Balance between room visibility and character face clarity

AVOID THESE DEVIATIONS FROM model1.png:
- Do NOT use different camera angles than model1.png
- Do NOT change the perspective from model1.png
- Do NOT use different lighting than model1.png
- Do NOT alter the composition style of model1.png
- Do NOT use different dollhouse proportions than model1.png
- Do NOT deviate from model1.png visual style in any way
- Must be IDENTICAL to model1.png in all aspects

AVOID THESE CHARACTER ISSUES:
- Do NOT show character from back or side view
- Do NOT hide the character's face or make it unclear
- Do NOT add facial hair if the person doesn't have any
- Do NOT change the person's facial structure or features
- Do NOT use anime or cartoon style
- Do NOT make the character too small in the wide frame

The final image must be a PERFECT REPLICA of model1.png style and composition. Camera positioned at the EXACT same distance and angle as model1.png. The dollhouse scene must match model1.png in every detail - same perspective, same lighting, same proportions, same overall visual style. The character's face must remain CLEARLY VISIBLE and recognizable while maintaining the EXACT style of model1.png. Create an image that is INDISTINGUISHABLE from model1.png in terms of composition, perspective, and quality, but with the new character and occupation-specific environment. This must be a PERFECT MATCH to model1.png - no deviations allowed."""

        print(f"✅ Dollhouse prompt created: {len(prompt)} characters")
        return prompt
    
    def _generate_with_gemini20(self, prompt, face_path, card_info=None, variant=1):
        """Generate image using Gemini 2.0 Flash Preview Image Generation"""
        try:
            print("🎨 Generating with Gemini 2.0 Flash Preview Image Generation...")
            
            # Load face image for reference
            face_image = Image.open(face_path)
            
            # Prepare content for Gemini 2.0
            contents = [
                prompt,
                face_image
            ]
            
            # Generate content with Gemini 2.0
            if NEW_SDK:
                # Use new SDK with proper response modalities
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=contents,
                    config=types.GenerateContentConfig(
                        response_modalities=['TEXT', 'IMAGE']
                    )
                )
            else:
                # Use legacy SDK - simple generation
                response = self.model.generate_content(contents)
            
            # Process response
            image_data = None
            text_response = ""
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    text_response = part.text
                    print(f"📝 Gemini response: {text_response[:200]}...")
                elif hasattr(part, 'inline_data') and part.inline_data:
                    if NEW_SDK:
                        image_data = part.inline_data.data
                    else:
                        image_data = part.inline_data.data
                    print("🖼️ Image data received from Gemini 2.0")
            
            if image_data:
                # Save generated image with variant number
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_part = card_info.get('name', 'person').lower().replace(' ', '_') if card_info else 'person'
                output_path = Path("outputs") / f"gemini20_dollhouse_{name_part}_{timestamp}_v{variant}.png"
                output_path.parent.mkdir(exist_ok=True)
                
                # Convert image data to PIL Image
                if isinstance(image_data, str):
                    # Base64 string
                    image_bytes = base64.b64decode(image_data)
                    image = Image.open(BytesIO(image_bytes))
                else:
                    # Direct bytes
                    image = Image.open(BytesIO(image_data))

                image.save(output_path)
                
                print(f"✅ Image saved: {output_path}")
                
                return {
                    'success': True,
                    'image_path': str(output_path),
                    'text_response': text_response
                }
            else:
                return {
                    'success': False,
                    'error': 'No image data received from Gemini 2.0'
                }
                
        except Exception as e:
            print(f"❌ Gemini 2.0 generation error: {e}")
            return {
                'success': False,
                'error': f"Generation failed: {str(e)}"
            }

def test_gemini20_generator():
    """Test Gemini 2.0 Image Generator"""
    print("🧪 Testing Gemini 2.0 Image Generator")
    print("=" * 50)
    
    generator = Gemini20ImageGenerator()
    
    # Test data
    face_path = "face1.jpg"
    card_info = {
        'name': 'Dr. Sarah Johnson',
        'title': 'Senior Software Engineer',
        'company': 'TechCorp Solutions',
        'email': '<EMAIL>',
        'phone': '+****************'
    }
    
    if Path(face_path).exists():
        print(f"\n🔥 Testing Gemini 2.0 generation...")
        result = generator.generate_dollhouse_image(face_path, card_info)
        
        if result.get('success'):
            print("\n✅ GEMINI 2.0 GENERATION SUCCESSFUL!")
            print(f"   Image: {result['image_path']}")
            print(f"   Model: {result['model']}")
            print(f"   Quality: {result['quality_score']}")
        else:
            print(f"\n❌ Generation failed: {result.get('error')}")
    else:
        print(f"❌ Test file not found: {face_path}")

if __name__ == "__main__":
    test_gemini20_generator()
