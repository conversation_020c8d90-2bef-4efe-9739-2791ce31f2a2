import cv2
import numpy as np
from PIL import Image
import google.generativeai as genai
import os
import re
import json

class OCRProcessor:
    def __init__(self):
        # Cấu hình Gemini API
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBJGGJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ')  # Thay bằng API key thực
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
    
    def extract_card_info(self, image_path):
        """
        Trích xuất thông tin từ name card sử dụng Gemini Vision API
        """
        try:
            # Đọc và xử lý ảnh
            image = Image.open(image_path)
            
            # Prompt cho Gemini để trích xuất thông tin
            prompt = """
            Analyze this business card image and extract the following information in JSON format:
            {
                "name": "Full name of the person",
                "title": "Job title or position", 
                "company": "Company name",
                "email": "Email address",
                "phone": "Phone number",
                "address": "Address if available",
                "website": "Website if available",
                "other_info": "Any other relevant information"
            }
            
            Please extract the text accurately and return only the JSON object. If any field is not found, use empty string "".
            """
            
            # Gửi request đến Gemini
            response = self.model.generate_content([prompt, image])
            
            # Parse JSON response
            try:
                # Tìm JSON trong response
                response_text = response.text
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = response_text[json_start:json_end]
                    extracted_info = json.loads(json_str)
                else:
                    # Fallback parsing
                    extracted_info = self._parse_text_response(response_text)
                
                return extracted_info
                
            except json.JSONDecodeError:
                # Fallback parsing nếu JSON không hợp lệ
                return self._parse_text_response(response.text)
                
        except Exception as e:
            print(f"Error in OCR processing: {str(e)}")
            return {
                "name": "",
                "title": "",
                "company": "",
                "email": "",
                "phone": "",
                "address": "",
                "website": "",
                "other_info": f"Error: {str(e)}"
            }
    
    def _parse_text_response(self, text):
        """
        Fallback method để parse text response nếu JSON parsing thất bại
        """
        info = {
            "name": "",
            "title": "",
            "company": "",
            "email": "",
            "phone": "",
            "address": "",
            "website": "",
            "other_info": ""
        }
        
        # Regex patterns để tìm thông tin
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        phone_pattern = r'[\+]?[1-9]?[0-9]{7,15}'
        website_pattern = r'www\.[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}'
        
        # Tìm email
        email_match = re.search(email_pattern, text)
        if email_match:
            info["email"] = email_match.group()
        
        # Tìm phone
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            info["phone"] = phone_match.group()
        
        # Tìm website
        website_match = re.search(website_pattern, text)
        if website_match:
            info["website"] = website_match.group()
        
        # Lưu toàn bộ text vào other_info
        info["other_info"] = text
        
        return info
    
    def preprocess_image(self, image_path):
        """
        Tiền xử lý ảnh để cải thiện độ chính xác OCR
        """
        # Đọc ảnh
        image = cv2.imread(image_path)
        
        # Chuyển sang grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Áp dụng threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Khử noise
        denoised = cv2.medianBlur(thresh, 3)
        
        # Lưu ảnh đã xử lý
        processed_path = image_path.replace('.jpg', '_processed.jpg')
        cv2.imwrite(processed_path, denoised)
        
        return processed_path
