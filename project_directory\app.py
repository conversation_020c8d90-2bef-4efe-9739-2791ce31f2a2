import cv2
from flask import Flask, render_template, Response, request, jsonify
import threading
import time
import os

app = Flask(__name__)

UPLOAD_FOLDER = 'static/img'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

camera_0 = None  # Logitech (bên tr<PERSON>i)
camera_1 = None  # Laptop (bên ph<PERSON>i)
output_frame_0 = None
output_frame_1 = None
lock_0 = threading.Lock()
lock_1 = threading.Lock()

initial_focus_value = 0  # Logitech manual focus

def initialize_camera(camera_index, focus_value=None):
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_index}")
        return None

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    if focus_value is not None:
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
        cap.set(cv2.CAP_PROP_FOCUS, focus_value)
        print(f"Camera {camera_index} - Focus set to {focus_value}")

    return cap

def generate_frames(camera_id):
    global camera_0, camera_1, output_frame_0, output_frame_1

    while True:
        cap = camera_0 if camera_id == 0 else camera_1
        lock = lock_0 if camera_id == 0 else lock_1

        if cap is None or not cap.isOpened():
            print(f"Camera {camera_id} chưa sẵn sàng.")
            time.sleep(2)
            continue

        ret, frame = cap.read()
        if not ret:
            print(f"Lỗi đọc camera {camera_id}. Đang khởi động lại...")
            cap.release()
            if camera_id == 0:
                with lock_0:
                    camera_0 = initialize_camera(0, initial_focus_value)
            else:
                with lock_1:
                    camera_1 = initialize_camera(1)
            time.sleep(1)
            continue

        with lock:
            if camera_id == 0:
                output_frame_0 = frame.copy()
            else:
                output_frame_1 = frame.copy()

        ret, buffer = cv2.imencode('.jpg', frame)
        frame = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture_step/<int:camera_id>', methods=['POST'])
def capture_step(camera_id):
    global output_frame_0, output_frame_1

    timestamp = int(time.time())
    filename = None

    if camera_id == 0:
        with lock_0:
            if output_frame_0 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'logitech_left_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_0)
    elif camera_id == 1:
        with lock_1:
            if output_frame_1 is not None:
                filename = os.path.join(UPLOAD_FOLDER, f'laptop_right_{timestamp}.jpg')
                cv2.imwrite(filename, output_frame_1)

    if filename:
        return jsonify({'status': 'success', 'image_path': filename})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    global camera_0, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_0:
        if camera_0 and camera_0.isOpened():
            camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Focus mới cho Logitech: {new_focus_value}")
            return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

def setup_cameras():
    global camera_0, camera_1
    print("Đang khởi tạo camera...")
    camera_0 = initialize_camera(0, initial_focus_value)  # Logitech
    camera_1 = initialize_camera(1)  # Laptop

    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()

if __name__ == '__main__':
    with app.app_context():
        setup_cameras()
    app.run(host='0.0.0.0', port=5000, debug=False)
