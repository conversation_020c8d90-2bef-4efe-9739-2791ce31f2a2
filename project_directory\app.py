import cv2
from flask import Flask, render_template, Response, request, jsonify
import threading
import time
import os
import numpy as np

app = Flask(__name__)

# Đ<PERSON><PERSON> bảo thư mục lưu ảnh tồn tại
UPLOAD_FOLDER = 'static/img'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# Biến toàn cục để lưu trữ đối tượng VideoCapture và các lock
camera_0 = None
camera_1 = None
output_frame_0 = None
output_frame_1 = None
lock_0 = threading.Lock()
lock_1 = threading.Lock()

# Giá trị focus ban đầu cho Camera 1 (Logitech C270)
initial_focus_value = 100

def initialize_camera(camera_index, focus_value=None):
    """Khởi tạo camera và thiết lập các thuộc tính."""
    cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW) # Sử dụng cv2.CAP_DSHOW cho Windows
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_index}")
        return None

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    if focus_value is not None:
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0) # Tắt autofocus
        cap.set(cv2.CAP_PROP_FOCUS, focus_value) # Đặt giá trị focus
        print(f"Camera {camera_index} - Autofocus: {cap.get(cv2.CAP_PROP_AUTOFOCUS)}, Focus: {cap.get(cv2.CAP_PROP_FOCUS)}")

    return cap

def generate_frames(camera_id):
    """Tạo các khung hình từ camera để stream."""
    global camera_0, camera_1, output_frame_0, output_frame_1, lock_0, lock_1

    cap = None
    if camera_id == 0:
        cap = camera_0
        current_lock = lock_0
        current_output_frame = output_frame_0 # Dùng tạm để gán
    elif camera_id == 1:
        cap = camera_1
        current_lock = lock_1
        current_output_frame = output_frame_1 # Dùng tạm để gán

    while True:
        # Lấy cap từ biến global (đã được khởi tạo)
        if camera_id == 0:
            cap = camera_0
            current_lock = lock_0
        elif camera_id == 1:
            cap = camera_1
            current_lock = lock_1

        if cap is None or not cap.isOpened():
            # In cảnh báo nếu camera chưa sẵn sàng, nhưng không cố gắng khởi tạo lại ở đây
            # Camera đã được khởi tạo trong setup_cameras()
            print(f"Camera {camera_id} chưa sẵn sàng hoặc bị mất kết nối. Đang chờ...")
            time.sleep(2)
            continue

        ret, frame = cap.read()
        if not ret:
            print(f"Không thể đọc khung hình từ camera {camera_id}. Đang thử lại sau khi reset...")
            # Đóng và mở lại camera để reset
            cap.release()
            if camera_id == 0:
                with lock_0:
                    camera_0 = initialize_camera(0, initial_focus_value)
            elif camera_id == 1:
                with lock_1:
                    camera_1 = initialize_camera(1)
            time.sleep(1)
            continue

        with current_lock:
            if camera_id == 0:
                output_frame_0 = frame.copy()
            elif camera_id == 1:
                output_frame_1 = frame.copy()

        ret, buffer = cv2.imencode('.jpg', frame)
        frame = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    return Response(generate_frames(camera_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture', methods=['POST'])
def capture_image():
    global output_frame_0, output_frame_1, lock_0, lock_1

    timestamp = int(time.time())
    image_paths = []

    with lock_0:
        if output_frame_0 is not None:
            filename_0 = os.path.join(UPLOAD_FOLDER, f'camera1_{timestamp}.jpg')
            cv2.imwrite(filename_0, output_frame_0)
            image_paths.append(filename_0)
            print(f"Đã lưu ảnh từ Camera 1: {filename_0}")
        else:
            print("Không có khung hình từ Camera 1 để chụp.")

    with lock_1:
        if output_frame_1 is not None:
            filename_1 = os.path.join(UPLOAD_FOLDER, f'camera2_{timestamp}.jpg')
            cv2.imwrite(filename_1, output_frame_1)
            image_paths.append(filename_1)
            print(f"Đã lưu ảnh từ Camera 2: {filename_1}")
        else:
            print("Không có khung hình từ Camera 2 để chụp.")

    if image_paths:
        return jsonify({'status': 'success', 'message': 'Ảnh đã được chụp và lưu.', 'image_paths': image_paths})
    else:
        return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh từ cả hai camera.'}), 500

@app.route('/adjust_focus', methods=['POST'])
def adjust_focus():
    global camera_0, lock_0, initial_focus_value
    data = request.get_json()
    new_focus_value = data.get('focus_value')

    if new_focus_value is None:
        return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

    try:
        new_focus_value = int(new_focus_value)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

    with lock_0:
        if camera_0 and camera_0.isOpened():
            camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
            initial_focus_value = new_focus_value
            print(f"Đã điều chỉnh focus Camera 1 lên: {camera_0.get(cv2.CAP_PROP_FOCUS)}")
            return jsonify({'status': 'success', 'message': f'Focus Camera 1 đã được điều chỉnh thành {new_focus_value}.'})
        else:
            return jsonify({'status': 'error', 'message': 'Camera 1 không hoạt động hoặc không tìm thấy.'}), 500

# Hàm khởi tạo camera, không còn là decorator
def setup_cameras():
    """Khởi tạo camera khi ứng dụng Flask bắt đầu."""
    global camera_0, camera_1
    print("Khởi tạo camera...")
    camera_0 = initialize_camera(0, initial_focus_value) # Camera Logitech C270 (thường là index 0)
    camera_1 = initialize_camera(1) # Camera Laptop (thường là index 1)

    # Bắt đầu luồng đọc khung hình cho mỗi camera
    threading.Thread(target=lambda: list(generate_frames(0)), daemon=True).start()
    threading.Thread(target=lambda: list(generate_frames(1)), daemon=True).start()


if __name__ == '__main__':
    # Gọi setup_cameras trong app context
    with app.app_context():
        setup_cameras()
    app.run(host='0.0.0.0', port=5000, debug=False)