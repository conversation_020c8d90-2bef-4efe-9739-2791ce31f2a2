<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Streaming</title>
    <style>
        #camera1, #camera2 {
            width: 45%;
            margin: 2%;
            border: 1px solid #000;
        }
        button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h2>Camera Streaming</h2>
    <div>
        <img id="camera1" src="/video_feed/0" width="640" height="480">
        <img id="camera2" src="/video_feed/1" width="640" height="480">

    </div>
    <button onclick="captureImages()">Chụp ảnh</button>

    <script>
        let camera1Stream, camera2Stream;

        // Hàm để lấy và khởi tạo các camera
        async function startCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices(); // L<PERSON><PERSON> tất cả thiết bị video

                let camera1, camera2;

                // Tìm camera Logitech C270 (USB)
                camera1 = devices.find(device => device.kind === 'videoinput' && device.label.includes('Logitech C270'));
                // Tìm camera Laptop
                camera2 = devices.find(device => device.kind === 'videoinput' && device.label.includes('HD Camera'));

                if (camera1 && camera2) {
                    // Khởi tạo camera Logitech C270 (deviceId từ camera1)
                    const camera1Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera1.deviceId }
                    });
                    document.getElementById('camera1').srcObject = camera1Stream;
                    camera1Stream = camera1Stream;

                    // Khởi tạo camera Laptop (deviceId từ camera2)
                    const camera2Stream = await navigator.mediaDevices.getUserMedia({
                        video: { deviceId: camera2.deviceId }
                    });
                    document.getElementById('camera2').srcObject = camera2Stream;
                    camera2Stream = camera2Stream;
                } else {
                    alert('Không tìm thấy các camera phù hợp.');
                }
            } catch (error) {
                console.error('Error accessing cameras:', error);
            }
        }

        // Chụp ảnh từ hai camera và gửi lên backend
        async function captureImages() {
            const canvas1 = document.createElement('canvas');
            const canvas2 = document.createElement('canvas');
            canvas1.getContext('2d').drawImage(document.getElementById('camera1'), 0, 0);
            canvas2.getContext('2d').drawImage(document.getElementById('camera2'), 0, 0);

            const img1 = canvas1.toDataURL('image/png');
            const img2 = canvas2.toDataURL('image/png');

            // Gửi ảnh tới backend Flask để xử lý
            sendToBackend(img1, img2);
        }

        // Gửi ảnh đến backend Flask
        function sendToBackend(img1, img2) {
            fetch('/process_images', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ img1, img2 })
            })
            .then(response => response.json())
            .then(data => {
                console.log("Kết quả xử lý", data);
                // Hiển thị ảnh kết quả sau khi xử lý (nếu cần)
            })
            .catch(error => console.error('Error:', error));
        }

        // Bắt đầu streaming các camera khi load trang
        startCameras(); 
    </script>
</body>
</html>
