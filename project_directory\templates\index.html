<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Streaming</title>
    <style>
        #camera1, #camera2 {
            width: 45%;
            margin: 2%;
            border: 1px solid #000;
        }
        button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h2>Camera Streaming</h2>

    <!-- LOGITECH (ID 0) nằm bên trái, LAPTOP (ID 1) nằm bên phải -->
    <div>
        <img id="camera1" src="/video_feed/0" width="640" height="480">
        <img id="camera2" src="/video_feed/1" width="640" height="480">
    </div>

    <button onclick="captureImages()">Chụp ảnh</button>

    <script>
        let camera1Stream, camera2Stream;

        // Khởi tạo cameras (nếu dùng WebRTC thì cần deviceId; ở đây bạn dùng stream từ server rồi)
        async function startCameras() {
            console.log("Streaming đã được khởi tạo qua /video_feed/* - không cần WebRTC khởi tạo lại");
        }

        async function captureImages() {
            fetch('/capture', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message || 'Đã chụp ảnh');
                console.log(data.image_paths);
            })
            .catch(error => console.error('Lỗi chụp ảnh:', error));
        }

        startCameras(); // thực tế không cần nếu dùng src="/video_feed/*"
    </script>
</body>
</html>
