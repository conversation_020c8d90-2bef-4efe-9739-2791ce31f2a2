#!/usr/bin/env python3
"""
Script để chạy server mà không bị auto-reload
"""

import os
import sys
from app import app, camera_manager

def main():
    print("🚀 Starting AI Card Visit Generator Server...")
    print("📷 Camera will be initialized on startup...")
    
    try:
        # Chạy server với production settings
        app.run(
            debug=False,
            host='0.0.0.0', 
            port=5000,
            threaded=True,
            use_reloader=False  # Tắt auto-reload
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
    finally:
        print("🧹 Cleaning up camera resources...")
        try:
            camera_manager.release_cameras()
        except:
            pass
        print("✅ Cleanup completed")

if __name__ == '__main__':
    main()
