"""
AI Image Generation Service
Handles multiple AI APIs for anime/manga style generation
"""
import asyncio
import requests
import base64
import os
from datetime import datetime
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
from config.settings import *

class AIImageGenerator:
    def __init__(self):
        self.openai_key = OPENAI_API_KEY
        self.hf_key = HUGGINGFACE_API_KEY
        self.replicate_key = REPLICATE_API_KEY
        
    async def generate_anime_portrait(self, face_image_path, style='base'):
        """
        Generate anime-style portrait from face image
        """
        print(f"🎨 Starting anime generation with style: {style}")
        
        # Try APIs in order of preference
        generators = [
            self._generate_with_openai,
            self._generate_with_huggingface,
            self._generate_with_replicate,
            self._generate_demo_anime
        ]
        
        for generator in generators:
            try:
                result = await generator(face_image_path, style)
                if result and result.get('success'):
                    return result
            except Exception as e:
                print(f"❌ Generator {generator.__name__} failed: {e}")
                continue
        
        # Final fallback
        return await self._generate_demo_anime(face_image_path, style)
    
    async def _generate_with_openai(self, face_image_path, style):
        """Generate with OpenAI DALL-E 3"""
        if not self.openai_key:
            raise Exception("OpenAI API key not configured")
            
        print("🤖 Generating with OpenAI DALL-E 3...")
        
        # Analyze face for prompt enhancement
        face_description = self._analyze_face_features(face_image_path)
        prompt = self._create_enhanced_prompt(face_description, style)
        
        headers = {
            "Authorization": f"Bearer {self.openai_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": AI_GENERATION_CONFIG['openai']['model'],
            "prompt": prompt,
            "n": 1,
            "size": AI_GENERATION_CONFIG['openai']['size'],
            "quality": AI_GENERATION_CONFIG['openai']['quality'],
            "style": AI_GENERATION_CONFIG['openai']['style']
        }
        
        response = requests.post(
            "https://api.openai.com/v1/images/generations",
            headers=headers,
            json=payload,
            timeout=AI_GENERATION_CONFIG['openai']['timeout']
        )
        
        if response.status_code == 200:
            data = response.json()
            image_url = data['data'][0]['url']
            
            # Download and save
            output_path = await self._download_and_save(image_url, 'openai_anime')
            
            return {
                'success': True,
                'image_path': output_path,
                'prompt_used': prompt,
                'api_used': 'OpenAI DALL-E 3',
                'generation_time': '10-20 seconds'
            }
        
        raise Exception(f"OpenAI API error: {response.status_code}")
    
    async def _generate_with_huggingface(self, face_image_path, style):
        """Generate with Hugging Face Stable Diffusion"""
        if not self.hf_key:
            raise Exception("Hugging Face API key not configured")
            
        print("🤗 Generating with Hugging Face...")
        
        face_description = self._analyze_face_features(face_image_path)
        prompt = self._create_enhanced_prompt(face_description, style)
        
        api_url = f"https://api-inference.huggingface.co/models/{AI_GENERATION_CONFIG['huggingface']['model']}"
        
        headers = {
            "Authorization": f"Bearer {self.hf_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "num_inference_steps": AI_GENERATION_CONFIG['huggingface']['steps'],
                "guidance_scale": AI_GENERATION_CONFIG['huggingface']['guidance_scale'],
                "width": 1024,
                "height": 1024
            }
        }
        
        response = requests.post(
            api_url, 
            headers=headers, 
            json=payload, 
            timeout=AI_GENERATION_CONFIG['huggingface']['timeout']
        )
        
        if response.status_code == 200:
            timestamp = datetime.now().strftime("%H%M%S")
            output_path = OUTPUTS_DIR / f"hf_anime_{timestamp}.png"
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            return {
                'success': True,
                'image_path': str(output_path),
                'prompt_used': prompt,
                'api_used': 'Hugging Face SD',
                'generation_time': '5-15 seconds'
            }
        
        raise Exception(f"Hugging Face API error: {response.status_code}")
    
    async def _generate_with_replicate(self, face_image_path, style):
        """Generate with Replicate API"""
        if not self.replicate_key:
            raise Exception("Replicate API key not configured")
            
        print("🔄 Generating with Replicate...")
        
        # Implementation for Replicate API
        # This would require the replicate library
        raise Exception("Replicate implementation pending")
    
    async def _generate_demo_anime(self, face_image_path, style):
        """Generate high-quality anime demo using image processing"""
        print("🎨 Creating advanced anime demo...")
        
        try:
            # Load and process face image
            face_img = Image.open(face_image_path).convert('RGB')
            
            # Create anime-style canvas
            canvas_size = (1024, 1024)
            canvas = Image.new('RGB', canvas_size, '#F0F8FF')
            
            # Apply anime-style processing
            anime_result = self._apply_anime_effects(face_img, canvas, style)
            
            # Save result
            timestamp = datetime.now().strftime("%H%M%S")
            output_path = OUTPUTS_DIR / f"demo_anime_{timestamp}.png"
            anime_result.save(output_path, 'PNG', quality=95)
            
            return {
                'success': True,
                'image_path': str(output_path),
                'prompt_used': f'Advanced anime processing - {style} style',
                'api_used': 'Advanced Demo Generator',
                'generation_time': '2-5 seconds'
            }
            
        except Exception as e:
            print(f"❌ Demo generation error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _analyze_face_features(self, face_image_path):
        """Analyze face features for prompt enhancement"""
        try:
            face_img = Image.open(face_image_path)
            
            # Basic analysis (can be enhanced with face detection libraries)
            width, height = face_img.size
            aspect_ratio = width / height
            
            # Simple feature detection
            features = {
                'face_shape': 'oval' if aspect_ratio > 0.8 else 'round',
                'image_quality': 'high' if min(width, height) > 400 else 'medium',
                'lighting': 'good'  # Can be enhanced with actual analysis
            }
            
            return features
            
        except Exception as e:
            return {'face_shape': 'unknown', 'image_quality': 'medium', 'lighting': 'good'}
    
    def _create_enhanced_prompt(self, face_features, style):
        """Create enhanced prompt based on face analysis"""
        base_prompt = ANIME_PROMPTS.get(style, ANIME_PROMPTS['base'])
        
        # Enhance based on features
        enhancements = []
        
        if face_features.get('face_shape') == 'oval':
            enhancements.append("elegant oval face")
        elif face_features.get('face_shape') == 'round':
            enhancements.append("cute round face")
            
        if face_features.get('image_quality') == 'high':
            enhancements.append("ultra detailed")
            
        enhanced_prompt = base_prompt
        if enhancements:
            enhanced_prompt += ", " + ", ".join(enhancements)
            
        return enhanced_prompt
    
    def _apply_anime_effects(self, face_img, canvas, style):
        """Apply anime-style effects to face image"""
        # Resize face to fit canvas
        face_size = (600, 750)
        face_resized = face_img.resize(face_size, Image.Resampling.LANCZOS)
        
        # Apply anime-style filters
        # Enhance colors
        enhancer = ImageEnhance.Color(face_resized)
        face_enhanced = enhancer.enhance(1.3)
        
        # Smooth skin (anime effect)
        face_smooth = face_enhanced.filter(ImageFilter.SMOOTH_MORE)
        
        # Sharpen eyes and details
        face_sharp = face_smooth.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))
        
        # Create gradient background
        draw = ImageDraw.Draw(canvas)
        for y in range(canvas.size[1]):
            color_intensity = int(240 - (y * 40 / canvas.size[1]))
            color = (color_intensity, color_intensity + 8, 255)
            draw.line([(0, y), (canvas.size[0], y)], fill=color)
        
        # Position face on canvas
        face_x = (canvas.size[0] - face_size[0]) // 2
        face_y = (canvas.size[1] - face_size[1]) // 2 - 50
        
        # Paste with soft edges
        canvas.paste(face_sharp, (face_x, face_y))
        
        # Add anime-style decorative elements
        self._add_anime_decorations(canvas, style)
        
        return canvas
    
    def _add_anime_decorations(self, canvas, style):
        """Add anime-style decorative elements"""
        draw = ImageDraw.Draw(canvas)
        
        # Add sparkles or other anime elements based on style
        if style == 'creative':
            # Add sparkle effects
            import random
            for _ in range(20):
                x = random.randint(50, canvas.size[0] - 50)
                y = random.randint(50, canvas.size[1] - 50)
                size = random.randint(3, 8)
                draw.ellipse([x-size, y-size, x+size, y+size], fill='#FFD700', outline='#FFA500')
        
        # Add border frame
        border_width = 10
        draw.rectangle([0, 0, canvas.size[0], border_width], fill='#FFD700')
        draw.rectangle([0, canvas.size[1]-border_width, canvas.size[0], canvas.size[1]], fill='#FFD700')
        draw.rectangle([0, 0, border_width, canvas.size[1]], fill='#FFD700')
        draw.rectangle([canvas.size[0]-border_width, 0, canvas.size[0], canvas.size[1]], fill='#FFD700')
    
    async def _download_and_save(self, image_url, prefix):
        """Download image from URL and save locally"""
        response = requests.get(image_url, timeout=30)
        if response.status_code == 200:
            timestamp = datetime.now().strftime("%H%M%S")
            output_path = OUTPUTS_DIR / f"{prefix}_{timestamp}.png"
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            return str(output_path)
        
        raise Exception(f"Failed to download image: {response.status_code}")
