"""
Business Card Processing Service
Handles OCR, text extraction, and card composition
"""
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageEnhance
from datetime import datetime
import re
import json
from config.settings import *

class CardProcessor:
    def __init__(self):
        self.ocr_confidence = CARD_PROCESSING['ocr_confidence']
        self.template_size = CARD_PROCESSING['card_template_size']
        self.portrait_size = CARD_PROCESSING['portrait_size']
    
    def extract_card_info(self, card_image_path):
        """
        Extract information from business card using OCR
        """
        print(f"📄 Extracting information from card: {card_image_path}")
        
        try:
            # Load and preprocess image
            card_img = cv2.imread(card_image_path)
            processed_img = self._preprocess_for_ocr(card_img)
            
            # Extract text using OCR (mock implementation)
            extracted_text = self._perform_ocr(processed_img)
            
            # Parse extracted information
            card_info = self._parse_card_information(extracted_text)
            
            print(f"✅ Extracted card info: {card_info}")
            return card_info
            
        except Exception as e:
            print(f"❌ Card extraction error: {e}")
            return self._get_default_card_info()
    
    def _preprocess_for_ocr(self, image):
        """Preprocess image for better OCR results"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply threshold to get binary image
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _perform_ocr(self, image):
        """
        Perform OCR on preprocessed image
        Note: This is a mock implementation. In production, use pytesseract or similar
        """
        # Mock OCR results - in real implementation, use:
        # import pytesseract
        # text = pytesseract.image_to_string(image, lang='eng+vie')
        
        mock_text = """
        John Doe
        Senior Software Engineer
        TechCorp Solutions
        <EMAIL>
        +****************
        www.techcorp.com
        123 Tech Street, Silicon Valley, CA 94000
        """
        
        return mock_text
    
    def _parse_card_information(self, text):
        """Parse extracted text to identify different information types"""
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        card_info = {
            'name': '',
            'title': '',
            'company': '',
            'email': '',
            'phone': '',
            'website': '',
            'address': '',
            'raw_text': text
        }
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        # Phone pattern
        phone_pattern = r'[\+]?[1-9]?[\s\-\(\)]?[\d\s\-\(\)]{10,}'
        
        # Website pattern
        website_pattern = r'www\.[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}'
        
        for i, line in enumerate(lines):
            # Email detection
            if re.search(email_pattern, line):
                card_info['email'] = re.search(email_pattern, line).group()
            
            # Phone detection
            elif re.search(phone_pattern, line):
                card_info['phone'] = re.search(phone_pattern, line).group()
            
            # Website detection
            elif re.search(website_pattern, line):
                card_info['website'] = re.search(website_pattern, line).group()
            
            # Name (usually first line)
            elif i == 0 and not card_info['name']:
                card_info['name'] = line
            
            # Title (usually second line)
            elif i == 1 and not card_info['title']:
                card_info['title'] = line
            
            # Company (usually third line or after title)
            elif i == 2 and not card_info['company']:
                card_info['company'] = line
        
        return card_info
    
    def _get_default_card_info(self):
        """Return default card information when extraction fails"""
        return {
            'name': 'Professional',
            'title': 'AI Generated Portrait',
            'company': 'Anime Business Card',
            'email': '<EMAIL>',
            'phone': '+****************',
            'website': 'www.example.com',
            'address': 'Professional Address',
            'raw_text': 'Default information'
        }
    
    def compose_business_card(self, anime_portrait_path, card_info, style='professional'):
        """
        Compose final business card with anime portrait and extracted information
        """
        print(f"🎨 Composing business card with style: {style}")
        
        try:
            # Create card canvas
            card = Image.new('RGB', self.template_size, '#FFFFFF')
            
            # Load anime portrait
            anime_img = Image.open(anime_portrait_path).convert('RGBA')
            
            # Apply card style
            if style == 'professional':
                card = self._apply_professional_style(card, anime_img, card_info)
            elif style == 'creative':
                card = self._apply_creative_style(card, anime_img, card_info)
            elif style == 'elegant':
                card = self._apply_elegant_style(card, anime_img, card_info)
            else:
                card = self._apply_professional_style(card, anime_img, card_info)
            
            # Save final card
            timestamp = datetime.now().strftime("%H%M%S")
            output_path = OUTPUTS_DIR / f"business_card_{style}_{timestamp}.png"
            card.save(output_path, 'PNG', quality=95)
            
            print(f"✅ Business card created: {output_path}")
            
            return {
                'success': True,
                'card_path': str(output_path),
                'style_used': style,
                'card_info': card_info
            }
            
        except Exception as e:
            print(f"❌ Card composition error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _apply_professional_style(self, card, anime_img, card_info):
        """Apply professional business card style"""
        draw = ImageDraw.Draw(card)
        
        # Background gradient
        for y in range(card.size[1]):
            color_intensity = int(250 - (y * 20 / card.size[1]))
            color = (color_intensity, color_intensity + 2, color_intensity + 5)
            draw.line([(0, y), (card.size[0], y)], fill=color)
        
        # Resize and position anime portrait
        portrait_resized = anime_img.resize(self.portrait_size, Image.Resampling.LANCZOS)
        portrait_x = 50
        portrait_y = (card.size[1] - self.portrait_size[1]) // 2
        
        # Create circular mask for portrait
        mask = Image.new('L', self.portrait_size, 0)
        mask_draw = ImageDraw.Draw(mask)
        mask_draw.ellipse([0, 0, self.portrait_size[0], self.portrait_size[1]], fill=255)
        
        # Apply mask and paste portrait
        portrait_with_mask = Image.new('RGBA', self.portrait_size, (0, 0, 0, 0))
        portrait_with_mask.paste(portrait_resized, (0, 0))
        portrait_with_mask.putalpha(mask)
        
        card.paste(portrait_with_mask, (portrait_x, portrait_y), portrait_with_mask)
        
        # Add text information
        text_x = portrait_x + self.portrait_size[0] + 50
        text_y = portrait_y + 20
        
        # Name (large)
        draw.text((text_x, text_y), card_info.get('name', 'Professional'), 
                 fill='#2C3E50', font=None)
        
        # Title
        draw.text((text_x, text_y + 40), card_info.get('title', 'AI Generated Portrait'), 
                 fill='#34495E', font=None)
        
        # Company
        draw.text((text_x, text_y + 80), card_info.get('company', 'Anime Business'), 
                 fill='#7F8C8D', font=None)
        
        # Contact info
        draw.text((text_x, text_y + 140), f"📧 {card_info.get('email', '<EMAIL>')}", 
                 fill='#3498DB', font=None)
        
        draw.text((text_x, text_y + 170), f"📱 {card_info.get('phone', '+****************')}", 
                 fill='#3498DB', font=None)
        
        draw.text((text_x, text_y + 200), f"🌐 {card_info.get('website', 'www.example.com')}", 
                 fill='#3498DB', font=None)
        
        # Add decorative elements
        draw.rectangle([text_x - 10, text_y + 120, text_x + 400, text_y + 125], fill='#E74C3C')
        
        return card
    
    def _apply_creative_style(self, card, anime_img, card_info):
        """Apply creative business card style"""
        draw = ImageDraw.Draw(card)
        
        # Colorful gradient background
        for y in range(card.size[1]):
            r = int(255 - (y * 50 / card.size[1]))
            g = int(200 + (y * 30 / card.size[1]))
            b = int(255 - (y * 20 / card.size[1]))
            color = (max(0, min(255, r)), max(0, min(255, g)), max(0, min(255, b)))
            draw.line([(0, y), (card.size[0], y)], fill=color)
        
        # Position anime portrait with creative frame
        portrait_resized = anime_img.resize(self.portrait_size, Image.Resampling.LANCZOS)
        portrait_x = 50
        portrait_y = 50
        
        # Creative frame
        frame_padding = 10
        draw.rectangle([
            portrait_x - frame_padding, 
            portrait_y - frame_padding,
            portrait_x + self.portrait_size[0] + frame_padding,
            portrait_y + self.portrait_size[1] + frame_padding
        ], fill='#FFD700', outline='#FF6B6B', width=3)
        
        card.paste(portrait_resized, (portrait_x, portrait_y))
        
        # Add creative text layout
        text_x = portrait_x + self.portrait_size[0] + 30
        text_y = portrait_y
        
        # Colorful text
        colors = ['#E74C3C', '#9B59B6', '#3498DB', '#1ABC9C', '#F39C12']
        
        texts = [
            card_info.get('name', 'Creative Professional'),
            card_info.get('title', 'Anime Artist'),
            card_info.get('company', 'Creative Studio'),
            card_info.get('email', '<EMAIL>'),
            card_info.get('phone', '+****************')
        ]
        
        for i, text in enumerate(texts):
            color = colors[i % len(colors)]
            draw.text((text_x, text_y + i * 40), text, fill=color, font=None)
        
        # Add creative decorations
        import random
        for _ in range(15):
            x = random.randint(text_x + 200, card.size[0] - 50)
            y = random.randint(50, card.size[1] - 50)
            size = random.randint(5, 15)
            color = random.choice(colors)
            draw.ellipse([x-size, y-size, x+size, y+size], fill=color)
        
        return card
    
    def _apply_elegant_style(self, card, anime_img, card_info):
        """Apply elegant business card style"""
        draw = ImageDraw.Draw(card)
        
        # Elegant gradient (subtle)
        for y in range(card.size[1]):
            color_intensity = int(248 - (y * 8 / card.size[1]))
            color = (color_intensity, color_intensity, color_intensity + 2)
            draw.line([(0, y), (card.size[0], y)], fill=color)
        
        # Position portrait elegantly
        portrait_resized = anime_img.resize(self.portrait_size, Image.Resampling.LANCZOS)
        portrait_x = (card.size[0] - self.portrait_size[0]) // 2
        portrait_y = 50
        
        # Elegant border
        border_color = '#C0392B'
        draw.rectangle([
            portrait_x - 5, portrait_y - 5,
            portrait_x + self.portrait_size[0] + 5,
            portrait_y + self.portrait_size[1] + 5
        ], outline=border_color, width=2)
        
        card.paste(portrait_resized, (portrait_x, portrait_y))
        
        # Centered elegant text
        text_y = portrait_y + self.portrait_size[1] + 30
        
        # Center-aligned text
        name = card_info.get('name', 'Elegant Professional')
        title = card_info.get('title', 'Distinguished Portrait')
        company = card_info.get('company', 'Elegant Enterprises')
        
        # Calculate text positions for centering
        name_width = len(name) * 8  # Approximate width
        title_width = len(title) * 6
        company_width = len(company) * 6
        
        draw.text(((card.size[0] - name_width) // 2, text_y), name, 
                 fill='#2C3E50', font=None)
        
        draw.text(((card.size[0] - title_width) // 2, text_y + 30), title, 
                 fill='#34495E', font=None)
        
        draw.text(((card.size[0] - company_width) // 2, text_y + 60), company, 
                 fill='#7F8C8D', font=None)
        
        # Elegant separator line
        line_y = text_y + 90
        draw.line([(card.size[0]//4, line_y), (3*card.size[0]//4, line_y)], 
                 fill=border_color, width=2)
        
        # Contact info (centered)
        contact_y = line_y + 20
        email = card_info.get('email', '<EMAIL>')
        phone = card_info.get('phone', '+****************')
        
        email_width = len(email) * 6
        phone_width = len(phone) * 6
        
        draw.text(((card.size[0] - email_width) // 2, contact_y), email, 
                 fill='#8E44AD', font=None)
        
        draw.text(((card.size[0] - phone_width) // 2, contact_y + 25), phone, 
                 fill='#8E44AD', font=None)
        
        return card
