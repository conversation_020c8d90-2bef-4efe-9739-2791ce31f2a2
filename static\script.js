// Global variables
let currentStream = null;
let capturedFacePath = null;
let extractedInfo = null;
let generatedImages = [];
let currentStep = 1;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up event listeners...');
    setupEventListeners();
    showStep(1);
});

function setupEventListeners() {
    console.log('Setting up event listeners...');

    // File upload events
    const cardInput = document.getElementById('card-input');
    const uploadArea = document.getElementById('upload-area');

    if (cardInput) {
        console.log('Card input found, adding change listener');
        cardInput.addEventListener('change', function(e) {
            console.log('File selected:', e.target.files[0]);
            uploadCard();
        });
    } else {
        console.error('Card input not found!');
    }

    // Upload area click and drag events
    if (uploadArea) {
        uploadArea.addEventListener('click', function() {
            if (cardInput) cardInput.click();
        });

        // Drag and drop events
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                cardInput.files = files;
                uploadCard();
            }
        });
    }

    // Other event listeners
    const confirmInfoBtn = document.getElementById('confirm-info');
    const editInfoBtn = document.getElementById('edit-info');
    const startCameraBtn = document.getElementById('start-camera');
    const captureFaceBtn = document.getElementById('capture-face');
    const generateAIBtn = document.getElementById('generate-ai');

    // Step 1 edit buttons
    const editExtractedInfoBtn = document.getElementById('edit-extracted-info');
    const confirmExtractedInfoBtn = document.getElementById('confirm-extracted-info');
    const closeModalBtn = document.getElementById('close-modal');
    const cancelEditBtn = document.getElementById('cancel-edit');
    const saveEditBtn = document.getElementById('save-edit');

    if (confirmInfoBtn) confirmInfoBtn.addEventListener('click', confirmInformation);
    if (editInfoBtn) editInfoBtn.addEventListener('click', editInformation);
    if (startCameraBtn) startCameraBtn.addEventListener('click', startCamera);
    if (captureFaceBtn) captureFaceBtn.addEventListener('click', captureFace);
    if (generateAIBtn) generateAIBtn.addEventListener('click', generateAIImages);

    // Step 1 edit event listeners
    if (editExtractedInfoBtn) editExtractedInfoBtn.addEventListener('click', openEditModal);
    if (confirmExtractedInfoBtn) confirmExtractedInfoBtn.addEventListener('click', confirmExtractedInfo);
    if (closeModalBtn) closeModalBtn.addEventListener('click', closeEditModal);
    if (cancelEditBtn) cancelEditBtn.addEventListener('click', closeEditModal);
    if (saveEditBtn) saveEditBtn.addEventListener('click', saveEditedInfo);

    // Close modal when clicking outside
    const editModal = document.getElementById('edit-modal');
    if (editModal) {
        editModal.addEventListener('click', function(e) {
            if (e.target === editModal) {
                closeEditModal();
            }
        });
    }
}

function showStep(stepNumber) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Update step indicators
    document.querySelectorAll('.step').forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index + 1 < stepNumber) {
            step.classList.add('completed');
        } else if (index + 1 === stepNumber) {
            step.classList.add('active');
        }
    });
    
    // Show current section
    const sections = ['', 'card-section', 'confirmation-section', 'face-section', 'generate-section'];
    if (sections[stepNumber]) {
        document.getElementById(sections[stepNumber]).classList.add('active');
    }
    
    currentStep = stepNumber;
}

// STEP 1: Upload name card
async function uploadCard() {
    const cardInput = document.getElementById('card-input');
    const file = cardInput.files[0];
    if (!file) {
        console.log('No file selected');
        return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
        showError('File quá lớn! Vui lòng chọn file nhỏ hơn 10MB.');
        return;
    }

    console.log('Uploading file:', file.name, 'Size:', file.size);

    const formData = new FormData();
    formData.append('card_image', file);

    try {
        // Show upload progress
        showUploadProgress(true);
        hideUploadArea();

        console.log('Sending request to /upload_card...');
        const response = await fetch('/upload_card', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Upload result:', result);

        if (result.success) {
            extractedInfo = result.extracted_info;

            // Show card preview
            showCardPreview(result.card_url);

            // Show extracted info
            displayExtractedInfo(extractedInfo);

            showSuccess('Name card đã được upload và xử lý bằng Gemini thành công! Vui lòng kiểm tra thông tin.');

            // Don't auto-move to next step, let user confirm first

        } else {
            showError('Lỗi khi xử lý name card: ' + result.error);
            showUploadArea();
        }
    } catch (error) {
        console.error('Upload error:', error);
        showError('Lỗi kết nối: ' + error.message);
        showUploadArea();
    } finally {
        showUploadProgress(false);
    }
}

function showUploadProgress(show) {
    const uploadProgress = document.getElementById('upload-progress');
    if (uploadProgress) {
        uploadProgress.style.display = show ? 'block' : 'none';
    }
}

function hideUploadArea() {
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.style.display = 'none';
    }
}

function showUploadArea() {
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.style.display = 'block';
    }
}

function showCardPreview(cardUrl) {
    const cardPreview = document.getElementById('card-preview');
    const uploadedCard = document.getElementById('uploaded-card');
    if (uploadedCard && cardUrl) {
        uploadedCard.src = cardUrl;
        if (cardPreview) cardPreview.style.display = 'block';
    }
}

function displayExtractedInfo(info) {
    const extractedInfoDiv = document.getElementById('extracted-info');
    const infoContent = document.getElementById('info-content');

    let html = '';
    for (const [key, value] of Object.entries(info)) {
        if (value && value.trim() !== '') {
            const label = key.charAt(0).toUpperCase() + key.slice(1);
            html += `
                <div class="info-item">
                    <span class="info-label">${label}:</span>
                    <span>${value}</span>
                </div>
            `;
        }
    }

    infoContent.innerHTML = html;
    extractedInfoDiv.style.display = 'block';
}

// Step 1 Edit Functions
function openEditModal() {
    const modal = document.getElementById('edit-modal');
    if (modal && extractedInfo) {
        // Populate form with current data
        document.getElementById('edit-name').value = extractedInfo.name || '';
        document.getElementById('edit-title').value = extractedInfo.title || '';
        document.getElementById('edit-company').value = extractedInfo.company || '';
        document.getElementById('edit-email').value = extractedInfo.email || '';
        document.getElementById('edit-phone').value = extractedInfo.phone || '';
        document.getElementById('edit-address').value = extractedInfo.address || '';
        document.getElementById('edit-website').value = extractedInfo.website || '';

        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Prevent background scroll
    }
}

function closeEditModal() {
    const modal = document.getElementById('edit-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scroll
    }
}

function saveEditedInfo() {
    // Get edited values
    const editedInfo = {
        name: document.getElementById('edit-name').value.trim(),
        title: document.getElementById('edit-title').value.trim(),
        company: document.getElementById('edit-company').value.trim(),
        email: document.getElementById('edit-email').value.trim(),
        phone: document.getElementById('edit-phone').value.trim(),
        address: document.getElementById('edit-address').value.trim(),
        website: document.getElementById('edit-website').value.trim()
    };

    // Update global extractedInfo
    extractedInfo = editedInfo;

    // Update display
    displayExtractedInfo(extractedInfo);

    // Close modal
    closeEditModal();

    showSuccess('Thông tin đã được cập nhật thành công!');
}

function confirmExtractedInfo() {
    if (!extractedInfo) {
        showError('Không có thông tin để xác nhận!');
        return;
    }

    showSuccess('Thông tin đã được xác nhận! Chuyển sang bước chụp khuôn mặt.');
    showStep(3); // Skip step 2 (confirmation) since we already confirmed in step 1
}

// STEP 2: Confirmation
function showConfirmation(info) {
    const confirmationBox = document.getElementById('confirmation-box');
    const confirmationContent = document.getElementById('confirmation-content');
    
    let html = '<div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    for (const [key, value] of Object.entries(info)) {
        if (value && value.trim() !== '') {
            const label = key.charAt(0).toUpperCase() + key.slice(1);
            html += `<p><strong>${label}:</strong> ${value}</p>`;
        }
    }
    html += '</div>';
    
    confirmationContent.innerHTML = html;
    confirmationBox.style.display = 'block';
}

function confirmInformation() {
    showSuccess('Thông tin đã được xác nhận! Chuyển sang bước chụp khuôn mặt.');
    showStep(3);
}

function editInformation() {
    showStep(1);
    showSuccess('Quay lại bước 1 để upload lại name card.');
}

// STEP 3: Face capture
async function startCamera() {
    try {
        const video = document.getElementById('video');
        const startCameraBtn = document.getElementById('start-camera');
        const captureFaceBtn = document.getElementById('capture-face');

        if (!video) {
            throw new Error('Video element not found');
        }

        currentStream = await navigator.mediaDevices.getUserMedia({
            video: { width: 640, height: 480 }
        });
        video.srcObject = currentStream;

        if (startCameraBtn) startCameraBtn.disabled = true;
        if (captureFaceBtn) captureFaceBtn.disabled = false;

        showSuccess('Camera laptop đã được bật thành công!');
    } catch (error) {
        console.error('Camera error:', error);
        showError('Không thể truy cập camera laptop: ' + error.message);
    }
}

function captureFace() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const generateAIBtn = document.getElementById('generate-ai');

    if (!video || !canvas) {
        showError('Video hoặc canvas element không tìm thấy');
        return;
    }

    const context = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    context.drawImage(video, 0, 0);

    // Convert to blob and send to server
    canvas.toBlob(async (blob) => {
        const reader = new FileReader();
        reader.onload = async function(e) {
            const imageData = e.target.result;

            try {
                const response = await fetch('/capture_face', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ image: imageData })
                });

                const result = await response.json();

                if (result.success) {
                    capturedFacePath = result.face_path;

                    // Show preview
                    const facePreview = document.getElementById('face-preview');
                    const capturedFace = document.getElementById('captured-face');
                    if (capturedFace) {
                        capturedFace.src = imageData;
                        if (facePreview) facePreview.style.display = 'block';
                    }

                    showSuccess('Khuôn mặt đã được chụp thành công!');

                    // Stop camera
                    if (currentStream) {
                        currentStream.getTracks().forEach(track => track.stop());
                    }

                    // Move to AI generation step
                    setTimeout(() => {
                        showStep(4);
                        if (generateAIBtn) generateAIBtn.disabled = false;
                    }, 1500);

                } else {
                    showError('Lỗi khi chụp ảnh: ' + result.error);
                }
            } catch (error) {
                console.error('Capture error:', error);
                showError('Lỗi kết nối: ' + error.message);
            }
        };
        reader.readAsDataURL(blob);
    }, 'image/jpeg', 0.8);
}

// STEP 4: Generate AI dollhouse image
async function generateAIImages() {
    if (!capturedFacePath || !extractedInfo) {
        showError('Vui lòng hoàn thành các bước trước!');
        return;
    }

    const generateAIBtn = document.getElementById('generate-ai');

    try {
        showLoading(true);
        if (generateAIBtn) generateAIBtn.disabled = true;

        const response = await fetch('/generate_ai_image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                face_path: capturedFacePath,
                extracted_info: extractedInfo
            })
        });

        const result = await response.json();

        if (result.success) {
            generatedImages = result.images;
            displayResults();
            showSuccess('Ảnh AI dollhouse với text tích hợp đã được tạo thành công!');
        } else {
            showError('Lỗi khi tạo ảnh AI: ' + result.error);
        }

    } catch (error) {
        console.error('Generate AI error:', error);
        showError('Lỗi kết nối: ' + error.message);
    } finally {
        showLoading(false);
        if (generateAIBtn) generateAIBtn.disabled = false;
    }
}

function displayResults() {
    const resultsContainer = document.getElementById('results');
    const imageGallery = document.getElementById('image-gallery');
    
    let html = '';
    generatedImages.forEach((image, index) => {
        html += `
            <div class="image-item">
                <img src="${image.url}" alt="AI Generated Dollhouse ${index + 1}">
                <div class="info">
                    <h4>AI Dollhouse ${index + 1}</h4>
                    <p>Text tích hợp như model1.png</p>
                    <button class="btn" onclick="downloadImage('${image.filename}')">
                        Tải xuống
                    </button>
                </div>
            </div>
        `;
    });
    
    imageGallery.innerHTML = html;
    resultsContainer.style.display = 'block';
}

function downloadImage(filename) {
    const link = document.createElement('a');
    link.href = `/download/${filename}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showSuccess(message) {
    const alert = document.getElementById('success-alert');
    alert.textContent = message;
    alert.style.display = 'block';
    
    setTimeout(() => {
        alert.style.display = 'none';
    }, 5000);
}

function showError(message) {
    const alert = document.getElementById('error-alert');
    alert.textContent = message;
    alert.style.display = 'block';
    
    setTimeout(() => {
        alert.style.display = 'none';
    }, 5000);
}

function showLoading(show) {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}


