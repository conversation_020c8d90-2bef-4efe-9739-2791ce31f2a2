<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit - New Workflow</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28cdea 0%, #a8e6ff 50%, #e0f7ff 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(40, 205, 234, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(40, 205, 234, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(168, 230, 255, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0,0,0,0.12),
                0 0 0 1px rgba(255,255,255,0.2);
            overflow: hidden;
            animation: containerFloat 6s ease-in-out infinite;
        }

        @keyframes containerFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .header {
            background: linear-gradient(135deg, rgb(40, 205, 234) 0%, #1e90ff 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 8px;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.95;
            position: relative;
            z-index: 1;
            font-weight: 300;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            padding: 30px 20px;
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .workflow-steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            animation: slideShine 4s infinite;
        }

        @keyframes slideShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 20px 10px;
            position: relative;
            z-index: 1;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 16px;
            margin: 0 5px;
        }

        .step:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.6);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.8em;
            color: rgb(40, 205, 234);
            animation: bounce 2s infinite;
            z-index: 2;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(-50%); }
            40% { transform: translateY(-60%); }
            60% { transform: translateY(-55%); }
        }

        .step.active {
            background: linear-gradient(135deg, rgb(40, 205, 234) 0%, #1e90ff 100%);
            border-radius: 16px;
            color: white;
            font-weight: 600;
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(40, 205, 234, 0.4);
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 15px 35px rgba(40, 205, 234, 0.4); }
            to { box-shadow: 0 15px 35px rgba(40, 205, 234, 0.7); }
        }

        .step.completed {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border-radius: 16px;
            color: white;
            font-weight: 600;
            animation: completePulse 3s infinite;
        }

        @keyframes completePulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        
        .main-content {
            padding: 30px;
        }
        
        .step-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .step-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .upload-area {
            border: 3px dashed rgba(40, 205, 234, 0.3);
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            margin: 30px 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(40, 205, 234, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }

        .upload-area:hover {
            border-color: rgb(40, 205, 234);
            background: linear-gradient(135deg, rgba(40, 205, 234, 0.1) 0%, rgba(30, 144, 255, 0.1) 100%);
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(40, 205, 234, 0.2);
        }

        .upload-area:hover::before {
            opacity: 1;
        }

        .upload-area.dragover {
            border-color: rgb(40, 205, 234);
            background: linear-gradient(135deg, rgba(40, 205, 234, 0.2) 0%, rgba(30, 144, 255, 0.2) 100%);
            transform: scale(1.02);
            box-shadow: 0 20px 40px rgba(40, 205, 234, 0.3);
        }

        .camera-area {
            text-align: center;
            margin: 20px 0;
        }

        #face-video {
            border: 3px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        #camera-placeholder {
            background: #f8f9fa;
            color: #6c757d;
        }

        .navigation-controls {
            margin: 20px 0;
            text-align: center;
        }

        .navigation-controls .btn {
            margin: 0 10px;
        }

        .image-info {
            text-align: center;
            margin: 10px 0;
            font-size: 0.9em;
            color: #6c757d;
        }

        .download-controls {
            text-align: center;
            margin: 20px 0;
        }

        .download-controls .btn {
            margin: 5px;
        }

        #image-navigation h3 {
            text-align: center;
            color: #495057;
            margin-bottom: 20px;
        }

        .image-container {
            position: relative;
            display: inline-block;
            margin: 0 auto;
        }

        .info-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);  /* Black background with higher transparency */
            border: 2px solid rgba(255, 255, 255, 0.8);  /* White border */
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.5);
            max-width: 300px;
            font-size: 0.9em;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .info-overlay h4 {
            margin: 0 0 10px 0;
            color: #ffffff;  /* White text for black background */
            font-size: 1em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);  /* White border */
            padding-bottom: 5px;
        }

        .info-content {
            max-height: 200px;
            overflow-y: auto;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
            align-items: flex-start;
        }

        .info-label {
            font-weight: bold;
            color: #ffffff;  /* White text for black background */
            min-width: 80px;
            flex-shrink: 0;
        }

        .info-value {
            color: #ffffff;  /* White text for black background */
            word-break: break-word;
            margin-left: 5px;
        }

        .info-overlay.hidden {
            opacity: 0;
            transform: translateY(10px);
            pointer-events: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .info-overlay {
                position: static;
                margin: 20px auto;
                max-width: 90%;
            }
        }
        
        .btn {
            background: linear-gradient(135deg, rgb(40, 205, 234) 0%, #1e90ff 100%);
            color: white;
            border: none;
            padding: 14px 32px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(40, 205, 234, 0.3);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(40, 205, 234, 0.4);
            background: linear-gradient(135deg, rgb(60, 215, 244) 0%, #4169e1 100%);
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .btn:disabled {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn:disabled::before {
            display: none;
        }
        
        .info-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .edit-input {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px 12px;
            font-size: 1em;
            width: 300px;
        }
        
        .result-display {
            text-align: center;
            padding: 30px;
        }
        
        .result-image {
            max-width: 100%;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            margin: 20px 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .result-image:hover {
            transform: scale(1.02);
            box-shadow: 0 30px 60px rgba(0,0,0,0.25);
        }
        
        .status-message {
            padding: 18px 25px;
            border-radius: 15px;
            margin: 15px 0;
            font-weight: 600;
            font-size: 1.1em;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            animation: slideInRight 0.5s ease-out;
            position: relative;
            overflow: hidden;
        }

        .status-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmerStatus 2s infinite;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes shimmerStatus {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-success {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(139, 195, 74, 0.9) 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .status-error {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.9) 0%, rgba(233, 30, 99, 0.9) 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);
        }

        .status-info {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.9) 0%, rgba(3, 169, 244, 0.9) 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
        }
        
        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(40, 205, 234, 0.2);
            border-top: 4px solid rgb(40, 205, 234);
            border-radius: 50%;
            animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
            margin: 20px auto;
            position: relative;
        }

        .loading::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 2px solid rgba(40, 205, 234, 0.1);
            border-radius: 50%;
            animation: modernSpin 2s cubic-bezier(0.4, 0, 0.2, 1) infinite reverse;
        }

        @keyframes modernSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating particles animation */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(40, 205, 234, 0.3);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
        }

        /* Modern card hover effects */
        .info-display {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .info-display:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Floating particles background -->
    <div class="floating-particles" id="particles"></div>

    <div class="container">
        <div class="header">
            <h1>🎨 AI Card Visit</h1>
            <p>Smart AI • Fast Generation • Professional Results</p>
        </div>
        
        <div class="workflow-steps">
            <div class="step active" id="step1-indicator">
                <div style="font-size: 2em; margin-bottom: 8px;">📄</div>
                <div style="font-weight: 600;">Upload</div>
            </div>
            <div class="step" id="step2-indicator">
                <div style="font-size: 2em; margin-bottom: 8px;">🔍</div>
                <div style="font-weight: 600;">Extract</div>
            </div>
            <div class="step" id="step3-indicator">
                <div style="font-size: 2em; margin-bottom: 8px;">✅</div>
                <div style="font-weight: 600;">Confirm</div>
            </div>
            <div class="step" id="step4-indicator">
                <div style="font-size: 2em; margin-bottom: 8px;">👤</div>
                <div style="font-weight: 600;">Face</div>
            </div>
            <div class="step" id="step5-indicator">
                <div style="font-size: 2em; margin-bottom: 8px;">🎨</div>
                <div style="font-weight: 600;">Generate</div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Step 1: Upload Card -->
            <div class="step-content active" id="step1-content">
                <h2 style="text-align: center; color: rgb(40, 205, 234); margin-bottom: 30px;">📄 Upload Business Card</h2>
                <div class="upload-area" id="card-upload-area">
                    <div>
                        <div style="font-size: 3em; margin-bottom: 15px;">📷</div>
                        <h3 style="margin-bottom: 10px;">Drop your card here</h3>
                        <p style="color: #666; margin-bottom: 20px;">or click to browse</p>
                        <input type="file" id="card-file-input" accept="image/*" style="display: none;">
                        <button class="btn" onclick="document.getElementById('card-file-input').click()">
                            📁 Choose File
                        </button>
                    </div>
                </div>
                <div id="card-preview" class="hidden" style="text-align: center; margin: 30px 0;">
                    <h4 style="color: #666; margin-bottom: 15px;">✅ Card Uploaded</h4>
                    <img id="card-preview-img" style="max-width: 400px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                </div>
                <div style="text-align: center;">
                    <button class="btn" id="extract-info-btn" onclick="extractCardInfo()" disabled>
                        🔍 Extract Information
                    </button>
                </div>
            </div>
            
            <!-- Step 2: Check Info -->
            <div class="step-content" id="step2-content">
                <h2 style="text-align: center; color: rgb(40, 205, 234); margin-bottom: 30px;">🔍 Extracted Information</h2>
                <div id="extracted-info" class="info-display">
                    <!-- Extracted info will be displayed here -->
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="goToStep(3)">✅ Looks Good</button>
                    <button class="btn" onclick="editInformation()">✏️ Edit</button>
                    <button class="btn" onclick="goToStep(1)" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">🔄 Retry</button>
                </div>
            </div>
            
            <!-- Step 3: Confirm -->
            <div class="step-content" id="step3-content">
                <h2 style="text-align: center; color: rgb(40, 205, 234); margin-bottom: 30px;">✅ Edit & Confirm</h2>
                <div id="edit-info" class="info-display">
                    <!-- Editable info will be displayed here -->
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" id="save-info-btn" onclick="saveInformation()">💾 Save & Continue</button>
                    <button class="btn" onclick="goToStep(2)" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">⬅️ Back</button>
                </div>
            </div>
            
            <!-- Step 4: Capture Face -->
            <div class="step-content" id="step4-content">
                <h2 style="text-align: center; color: rgb(40, 205, 234); margin-bottom: 30px;">👤 Capture Your Face</h2>
                <div class="camera-area">
                    <div id="camera-container" style="text-align: center;">
                        <video id="face-video" autoplay playsinline style="width: 100%; max-width: 640px; border-radius: 20px; display: none; box-shadow: 0 15px 35px rgba(0,0,0,0.2);"></video>
                        <canvas id="face-canvas" style="display: none;"></canvas>
                        <div id="camera-placeholder" style="text-align: center; padding: 60px; border: 3px dashed rgba(40, 205, 234, 0.3); border-radius: 20px; margin: 20px 0; background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);">
                            <div style="font-size: 4em; margin-bottom: 15px;">📷</div>
                            <h3 style="color: rgb(40, 205, 234); margin-bottom: 10px;">Camera Ready</h3>
                            <p style="color: #666;">Click below to start</p>
                        </div>
                    </div>
                    <div style="text-align: center; margin: 30px 0;">
                        <button class="btn" id="start-camera-btn" onclick="startCamera()">
                            📷 Start Camera
                        </button>
                        <button class="btn" id="capture-face-btn" onclick="capturePhoto()" style="display: none;">
                            📸 Capture
                        </button>
                        <button class="btn" id="retake-photo-btn" onclick="retakePhoto()" style="display: none; background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);">
                            🔄 Retake
                        </button>
                        <button class="btn" id="stop-camera-btn" onclick="stopCamera()" style="display: none; background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                            ⏹️ Stop
                        </button>
                    </div>
                </div>
                <div id="face-preview" class="hidden" style="text-align: center; margin: 30px 0;">
                    <h4 style="color: #666; margin-bottom: 15px;">✅ Photo Captured</h4>
                    <img id="face-preview-img" style="max-width: 400px; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                </div>
                <div style="text-align: center;">
                    <button class="btn" id="generate-ai-btn" onclick="generateAIImage()" disabled>
                        🎨 Generate AI Image
                    </button>
                </div>
            </div>
            
            <!-- Step 5: Generate AI -->
            <div class="step-content" id="step5-content">
                <h2 style="text-align: center; color: rgb(40, 205, 234); margin-bottom: 30px;">🎨 AI Magic Complete!</h2>
                <div class="result-display">
                    <div id="generation-status" style="text-align: center; font-size: 1.2em; color: #666;"></div>
                    <div id="ai-result" class="hidden">
                        <div id="image-navigation">
                            <h3>Generated AI Images (<span id="current-image">1</span>/<span id="total-images">2</span>)</h3>
                            <div class="image-container">
                                <img id="ai-result-img" class="result-image">
                                <div id="extracted-info-overlay" class="info-overlay">
                                    <h4>📄 Card Information</h4>
                                    <div class="info-content">
                                        <div class="info-row">
                                            <span class="info-label">👤 Name:</span>
                                            <span class="info-value" id="overlay-name">-</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">🏢 Company:</span>
                                            <span class="info-value" id="overlay-company">-</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">💼 Title:</span>
                                            <span class="info-value" id="overlay-title">-</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">📞 Phone:</span>
                                            <span class="info-value" id="overlay-phone">-</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">📧 Email:</span>
                                            <span class="info-value" id="overlay-email">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="navigation-controls">
                                <button class="btn" id="prev-image-btn" onclick="showPreviousImage()">⬅️ Previous</button>
                                <button class="btn" id="next-image-btn" onclick="showNextImage()">➡️ Next</button>
                                <button class="btn" id="toggle-info-btn" onclick="toggleInfoOverlay()">👁️ Toggle Info</button>
                            </div>
                            <div class="image-info">
                                <p id="image-filename">Image 1: ai_dollhouse_session_v1.png</p>
                            </div>
                        </div>
                        <div class="download-controls">
                            <button class="btn" id="download-current-btn" onclick="downloadCurrentImage()">📥 Download Current Image</button>
                            <button class="btn" id="download-all-btn" onclick="downloadAllImages()">📦 Download All Images</button>
                            <button class="btn" onclick="startNewSession()">🆕 Start New Session</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status Messages -->
        <div id="status-messages"></div>
    </div>
    
    <script>
        let currentStep = 1;
        let sessionId = null;
        let cardInfo = null;
        let generatedImages = [];
        let currentImageIndex = 0;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            startNewSession();
            setupFileInputs();
            setupDragAndDrop();
            createFloatingParticles();

            // Initialize info overlay as hidden
            const overlay = document.getElementById('extracted-info-overlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }
        });

        // Create floating particles animation
        function createFloatingParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 15;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random positioning
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';

                // Random colors
                const colors = [
                    'rgba(40, 205, 234, 0.3)',
                    'rgba(30, 144, 255, 0.3)',
                    'rgba(120, 219, 255, 0.3)',
                    'rgba(255, 255, 255, 0.4)'
                ];
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];

                particlesContainer.appendChild(particle);
            }
        }
        
        function startNewSession() {
            fetch('/start_session', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        sessionId = data.session_id;
                        showStatus('New session started: ' + sessionId, 'success');
                        goToStep(1);
                    } else {
                        showStatus('Failed to start session: ' + data.error, 'error');
                    }
                });
        }
        
        function setupFileInputs() {
            document.getElementById('card-file-input').addEventListener('change', function(e) {
                handleCardFile(e.target.files[0]);
            });
        }
        
        function setupDragAndDrop() {
            const cardArea = document.getElementById('card-upload-area');

            cardArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                cardArea.classList.add('dragover');
            });

            cardArea.addEventListener('dragleave', function(e) {
                cardArea.classList.remove('dragover');
            });

            cardArea.addEventListener('drop', function(e) {
                e.preventDefault();
                cardArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleCardFile(files[0]);
                }
            });
        }
        
        function handleCardFile(file) {
            if (file) {
                const formData = new FormData();
                formData.append('card_image', file);
                
                showStatus('Uploading card image...', 'info');
                
                fetch('/upload_card', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('Card uploaded successfully', 'success');
                        showCardPreview(file);
                        document.getElementById('extract-info-btn').disabled = false;
                    } else {
                        showStatus('Upload failed: ' + data.error, 'error');
                    }
                });
            }
        }
        
        // Camera variables
        let stream = null;
        let video = null;
        let canvas = null;
        let capturedImageData = null;

        function startCamera() {
            video = document.getElementById('face-video');
            canvas = document.getElementById('face-canvas');

            navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: 'user'
                }
            })
            .then(function(mediaStream) {
                stream = mediaStream;
                video.srcObject = stream;
                video.play();

                // Show video and hide placeholder
                document.getElementById('camera-placeholder').style.display = 'none';
                video.style.display = 'block';

                // Update buttons
                document.getElementById('start-camera-btn').style.display = 'none';
                document.getElementById('capture-face-btn').style.display = 'inline-block';
                document.getElementById('stop-camera-btn').style.display = 'inline-block';

                showStatus('Camera started successfully', 'success');
            })
            .catch(function(error) {
                console.error('Camera error:', error);
                showStatus('Failed to access camera: ' + error.message, 'error');
            });
        }

        function capturePhoto() {
            if (!video || !canvas) return;

            // Set canvas size to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Get image data
            capturedImageData = canvas.toDataURL('image/jpeg', 0.8);

            // Show preview
            document.getElementById('face-preview-img').src = capturedImageData;
            document.getElementById('face-preview').classList.remove('hidden');

            // Update buttons
            document.getElementById('capture-face-btn').style.display = 'none';
            document.getElementById('retake-photo-btn').style.display = 'inline-block';

            // Upload captured image
            uploadCapturedFace();

            showStatus('Photo captured successfully', 'success');
        }

        function retakePhoto() {
            // Hide preview
            document.getElementById('face-preview').classList.add('hidden');
            capturedImageData = null;

            // Update buttons
            document.getElementById('capture-face-btn').style.display = 'inline-block';
            document.getElementById('retake-photo-btn').style.display = 'none';
            document.getElementById('generate-ai-btn').disabled = true;
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }

            // Hide video and show placeholder
            video.style.display = 'none';
            document.getElementById('camera-placeholder').style.display = 'block';

            // Reset buttons
            document.getElementById('start-camera-btn').style.display = 'inline-block';
            document.getElementById('capture-face-btn').style.display = 'none';
            document.getElementById('stop-camera-btn').style.display = 'none';
            document.getElementById('retake-photo-btn').style.display = 'none';

            showStatus('Camera stopped', 'info');
        }

        function uploadCapturedFace() {
            if (!capturedImageData) return;

            // Convert base64 to blob
            const byteCharacters = atob(capturedImageData.split(',')[1]);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/jpeg' });

            // Create form data
            const formData = new FormData();
            formData.append('face_image', blob, 'captured_face.jpg');

            showStatus('Uploading captured photo...', 'info');

            fetch('/capture_face', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('Face photo uploaded successfully', 'success');
                    document.getElementById('generate-ai-btn').disabled = false;
                } else {
                    showStatus('Upload failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showStatus('Upload error: ' + error.message, 'error');
            });
        }
        
        function showCardPreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('card-preview-img').src = e.target.result;
                document.getElementById('card-preview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
        

        
        function extractCardInfo() {
            showStatus('Extracting card information...', 'info');

            fetch('/extract_card_info', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cardInfo = data.card_info;
                        displayExtractedInfo(cardInfo);
                        showStatus('Information extracted successfully', 'success');
                        goToStep(2);
                    } else {
                        showStatus('Extraction failed: ' + data.error, 'error');
                    }
                });
        }
        
        function displayExtractedInfo(info) {
            const container = document.getElementById('extracted-info');
            container.innerHTML = '';
            
            const fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address'];
            fields.forEach(field => {
                const item = document.createElement('div');
                item.className = 'info-item';
                item.innerHTML = `
                    <span class="info-label">${field.charAt(0).toUpperCase() + field.slice(1)}:</span>
                    <span class="info-value">${info[field] || 'N/A'}</span>
                `;
                container.appendChild(item);
            });
        }
        
        function editInformation() {
            const container = document.getElementById('edit-info');
            container.innerHTML = '';
            
            const fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address'];
            fields.forEach(field => {
                const item = document.createElement('div');
                item.className = 'info-item';
                item.innerHTML = `
                    <span class="info-label">${field.charAt(0).toUpperCase() + field.slice(1)}:</span>
                    <input type="text" class="edit-input" id="edit-${field}" value="${cardInfo[field] || ''}" placeholder="Enter ${field}">
                `;
                container.appendChild(item);
            });
            
            goToStep(3);
        }
        
        function saveInformation() {
            const fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address'];
            const updatedInfo = {};

            fields.forEach(field => {
                const input = document.getElementById(`edit-${field}`);
                if (input) {
                    updatedInfo[field] = input.value;
                }
            });

            fetch('/update_card_info', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updatedInfo)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cardInfo = data.card_info;
                    showStatus('Information saved successfully', 'success');
                    goToStep(4);
                } else {
                    showStatus('Save failed: ' + data.error, 'error');
                }
            });
        }
        
        function generateAIImage() {
            showStatus('🎨 AI is creating your dollhouse image...', 'info');
            document.getElementById('generation-status').innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div class="loading"></div>
                    <h3 style="color: rgb(40, 205, 234); margin: 20px 0;">✨ Creating AI Magic ✨</h3>
                    <p style="color: #666;">This usually takes 15-30 seconds</p>
                    <div style="margin-top: 20px;">
                        <div style="background: rgba(40, 205, 234, 0.1); border-radius: 10px; padding: 15px; display: inline-block;">
                            <span style="font-size: 1.1em;">🎭 Analyzing your face...</span>
                        </div>
                    </div>
                </div>
            `;

            // Add progress animation
            setTimeout(() => {
                document.getElementById('generation-status').innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading"></div>
                        <h3 style="color: rgb(40, 205, 234); margin: 20px 0;">✨ Creating AI Magic ✨</h3>
                        <p style="color: #666;">This usually takes 15-30 seconds</p>
                        <div style="margin-top: 20px;">
                            <div style="background: rgba(40, 205, 234, 0.1); border-radius: 10px; padding: 15px; display: inline-block;">
                                <span style="font-size: 1.1em;">🏠 Building dollhouse scene...</span>
                            </div>
                        </div>
                    </div>
                `;
            }, 5000);

            setTimeout(() => {
                document.getElementById('generation-status').innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading"></div>
                        <h3 style="color: rgb(40, 205, 234); margin: 20px 0;">✨ Creating AI Magic ✨</h3>
                        <p style="color: #666;">Almost done...</p>
                        <div style="margin-top: 20px;">
                            <div style="background: rgba(40, 205, 234, 0.1); border-radius: 10px; padding: 15px; display: inline-block;">
                                <span style="font-size: 1.1em;">🎨 Adding final touches...</span>
                            </div>
                        </div>
                    </div>
                `;
            }, 15000);

            fetch('/generate_ai_image', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('🎉 AI image generated successfully!', 'success');
                        displayAIResult(data.ai_result, data.session_summary.session_id);
                        goToStep(5);
                    } else {
                        showStatus('❌ Generation failed: ' + data.error, 'error');
                        document.getElementById('generation-status').innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #dc3545;">
                                <h3>⚠️ Generation Failed</h3>
                                <p>Please try again or contact support</p>
                            </div>
                        `;
                    }
                });
        }
        
        function displayAIResult(result, sessionId) {
            document.getElementById('generation-status').innerHTML = '';

            // Setup image navigation
            generatedImages = result.session_image_paths || [result.session_primary_path];
            currentImageIndex = 0;

            // Update UI
            document.getElementById('total-images').textContent = generatedImages.length;
            updateImageDisplay();
            updateNavigationButtons();

            // Populate info overlay with extracted card information
            populateInfoOverlay();

            document.getElementById('ai-result').classList.remove('hidden');
        }

        function populateInfoOverlay() {
            if (cardInfo) {
                document.getElementById('overlay-name').textContent = cardInfo.name || '-';
                document.getElementById('overlay-company').textContent = cardInfo.company || '-';
                document.getElementById('overlay-title').textContent = cardInfo.title || '-';
                document.getElementById('overlay-phone').textContent = cardInfo.phone || '-';
                document.getElementById('overlay-email').textContent = cardInfo.email || '-';
                // Only 5 essential fields - removed website and address
            }
        }

        function toggleInfoOverlay() {
            const overlay = document.getElementById('extracted-info-overlay');
            const toggleBtn = document.getElementById('toggle-info-btn');

            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                toggleBtn.textContent = '👁️ Hide Info';
            } else {
                overlay.classList.add('hidden');
                toggleBtn.textContent = '👁️ Show Info';
            }
        }

        function updateImageDisplay() {
            if (generatedImages.length === 0) return;

            const currentImage = generatedImages[currentImageIndex];
            const imageNumber = currentImageIndex + 1;

            // Update image
            document.getElementById('ai-result-img').src = `/download_session_image/${sessionId}/${imageNumber}`;

            // Update info
            document.getElementById('current-image').textContent = imageNumber;
            document.getElementById('image-filename').textContent =
                `Image ${imageNumber}: ai_dollhouse_${sessionId}_v${imageNumber}.png`;
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prev-image-btn');
            const nextBtn = document.getElementById('next-image-btn');

            prevBtn.disabled = currentImageIndex === 0;
            nextBtn.disabled = currentImageIndex === generatedImages.length - 1;

            // Hide navigation if only one image
            if (generatedImages.length <= 1) {
                document.querySelector('.navigation-controls').style.display = 'none';
            } else {
                document.querySelector('.navigation-controls').style.display = 'block';
            }
        }

        function showPreviousImage() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateImageDisplay();
                updateNavigationButtons();
            }
        }

        function showNextImage() {
            if (currentImageIndex < generatedImages.length - 1) {
                currentImageIndex++;
                updateImageDisplay();
                updateNavigationButtons();
            }
        }

        function downloadCurrentImage() {
            const imageNumber = currentImageIndex + 1;
            window.open(`/download_session_image/${sessionId}/${imageNumber}`, '_blank');
        }

        function downloadAllImages() {
            for (let i = 1; i <= generatedImages.length; i++) {
                setTimeout(() => {
                    window.open(`/download_session_image/${sessionId}/${i}`, '_blank');
                }, i * 500); // Stagger downloads
            }
        }
        
        function goToStep(step) {
            // Hide all step contents
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Hide all step indicators
            document.querySelectorAll('.step').forEach(indicator => {
                indicator.classList.remove('active', 'completed');
            });
            
            // Show current step content
            document.getElementById(`step${step}-content`).classList.add('active');
            document.getElementById(`step${step}-indicator`).classList.add('active');
            
            // Mark previous steps as completed
            for (let i = 1; i < step; i++) {
                document.getElementById(`step${i}-indicator`).classList.add('completed');
            }
            
            currentStep = step;
        }
        
        function showStatus(message, type) {
            const container = document.getElementById('status-messages');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            
            container.appendChild(statusDiv);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
