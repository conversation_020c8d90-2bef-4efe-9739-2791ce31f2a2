<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .workflow {
            display: flex;
            justify-content: space-around;
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .step {
            text-align: center;
            flex: 1;
            padding: 0 20px;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -10px;
            top: 25px;
            font-size: 2em;
            color: #667eea;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: #667eea;
            color: white;
        }

        .step.completed .step-number {
            background: #28a745;
            color: white;
        }

        .main-content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
            display: none;
        }

        .section.active {
            display: block;
        }

        .section h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
            border-style: solid;
        }

        .upload-icon {
            color: #667eea;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .upload-area:hover .upload-icon {
            color: #764ba2;
            transform: scale(1.1);
        }

        .upload-area h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .upload-area p {
            color: #666;
            margin-bottom: 15px;
        }

        .browse-text {
            color: #667eea;
            font-weight: bold;
            text-decoration: underline;
        }

        .upload-progress {
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            animation: progress-animation 2s infinite;
        }

        @keyframes progress-animation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .card-preview-container {
            margin-top: 30px;
            text-align: center;
        }

        .image-preview-wrapper {
            position: relative;
            display: inline-block;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .change-image-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .change-image-btn:hover {
            background: rgba(118, 75, 162, 0.9);
            transform: scale(1.05);
        }

        .info-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .edit-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.3em;
        }

        .close-modal {
            background: none;
            border: none;
            color: white;
            font-size: 2em;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-modal:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
        }

        .preview-container {
            display: none;
            margin-top: 20px;
            text-align: center;
        }

        .preview-image {
            max-width: 400px;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .extracted-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }

        .info-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-label {
            font-weight: bold;
            color: #667eea;
            display: inline-block;
            width: 120px;
        }

        .camera-container {
            text-align: center;
            margin-bottom: 20px;
        }

        #video {
            width: 100%;
            max-width: 400px;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .confirmation-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .confirmation-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .results-container {
            display: none;
            margin-top: 30px;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .image-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .image-item:hover {
            transform: translateY(-5px);
        }

        .image-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .image-item .info {
            padding: 20px;
            text-align: center;
        }

        .loading {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .comparison-note {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .comparison-note h5 {
            color: #007bff;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Card Visit Generator</h1>
            <p>Quy trình: Card → Xác nhận thông tin → Face → AI Generation</p>
        </div>

        <div class="workflow">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <h4>Upload Name Card</h4>
                <p>Import từ laptop</p>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <h4>Xác nhận thông tin</h4>
                <p>Check & confirm</p>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <h4>Chụp khuôn mặt</h4>
                <p>Laptop camera</p>
            </div>
            <div class="step" id="step4">
                <div class="step-number">4</div>
                <h4>Tạo AI Image</h4>
                <p>Dollhouse + Text</p>
            </div>
        </div>

        <div class="main-content">


            <!-- Step 1: Upload Card -->
            <div class="section active" id="card-section">
                <h3>Bước 1: Upload Name Card</h3>

                <!-- Beautiful Upload Area -->
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                    </div>
                    <h4>Kéo thả ảnh name card vào đây</h4>
                    <p>hoặc <span class="browse-text">click để chọn file</span></p>
                    <small>Hỗ trợ: JPG, PNG, JPEG (tối đa 10MB)</small>
                    <input type="file" id="card-input" accept="image/*" style="display: none;">
                </div>

                <!-- Upload Progress -->
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p>Đang upload và xử lý...</p>
                </div>

                <!-- Card Preview -->
                <div class="card-preview-container" id="card-preview" style="display: none;">
                    <h4>Name card đã upload:</h4>
                    <div class="image-preview-wrapper">
                        <img id="uploaded-card" class="preview-image" alt="Uploaded card">
                        <button class="change-image-btn" onclick="document.getElementById('card-input').click()">
                            Đổi ảnh khác
                        </button>
                    </div>
                </div>

                <!-- Extracted Info -->
                <div class="extracted-info" id="extracted-info" style="display: none;">
                    <h4>Thông tin trích xuất bằng Gemini:</h4>
                    <div id="info-content"></div>
                    <div class="info-actions" style="text-align: center; margin-top: 20px;">
                        <button class="btn" id="edit-extracted-info">Chỉnh sửa thông tin</button>
                        <button class="btn btn-success" id="confirm-extracted-info">Xác nhận và tiếp tục</button>
                    </div>
                </div>

                <!-- Edit Info Modal -->
                <div class="edit-modal" id="edit-modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Chỉnh sửa thông tin name card</h3>
                            <button class="close-modal" id="close-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="edit-name">Tên:</label>
                                <input type="text" id="edit-name" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-title">Chức vụ:</label>
                                <input type="text" id="edit-title" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-company">Công ty:</label>
                                <input type="text" id="edit-company" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-email">Email:</label>
                                <input type="email" id="edit-email" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-phone">Số điện thoại:</label>
                                <input type="tel" id="edit-phone" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-address">Địa chỉ:</label>
                                <input type="text" id="edit-address" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="edit-website">Website:</label>
                                <input type="url" id="edit-website" class="form-input">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" id="cancel-edit">Hủy</button>
                            <button class="btn btn-success" id="save-edit">Lưu thay đổi</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Confirmation -->
            <div class="section" id="confirmation-section">
                <h3>Bước 2: Xác nhận thông tin</h3>
                <div class="confirmation-box" id="confirmation-box">
                    <h4>Vui lòng kiểm tra thông tin đã trích xuất:</h4>
                    <div id="confirmation-content"></div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" id="confirm-info">Thông tin đúng, tiếp tục</button>
                        <button class="btn" id="edit-info">Chỉnh sửa</button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Face Capture -->
            <div class="section" id="face-section">
                <h3>Bước 3: Chụp khuôn mặt</h3>
                <div class="camera-container">
                    <video id="video" autoplay></video>
                    <canvas id="canvas" style="display: none;"></canvas>
                </div>
                <div style="text-align: center;">
                    <button class="btn" id="start-camera">Bật Camera</button>
                    <button class="btn" id="capture-face" disabled>Chụp ảnh</button>
                </div>
                <div class="preview-container" id="face-preview">
                    <h4>Ảnh khuôn mặt đã chụp:</h4>
                    <img id="captured-face" class="preview-image" alt="Captured face">
                </div>
            </div>

            <!-- Step 4: AI Generation -->
            <div class="section" id="generate-section">
                <h3>Bước 4: Tạo ảnh AI Dollhouse</h3>
                <div style="text-align: center;">
                    <button class="btn" id="generate-ai" disabled>Tạo ảnh AI với text tích hợp</button>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <h4>Đang tạo ảnh AI dollhouse...</h4>
                    <p>Text sẽ được tích hợp vào ảnh như model1.png</p>
                </div>

                <div class="results-container" id="results">
                    <h4>Kết quả AI Dollhouse:</h4>
                    <div class="image-gallery" id="image-gallery"></div>
                </div>
            </div>

            <!-- Alerts -->
            <div class="alert alert-success" id="success-alert"></div>
            <div class="alert alert-error" id="error-alert"></div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
