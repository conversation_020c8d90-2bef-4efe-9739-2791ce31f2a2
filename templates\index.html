<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Card Visit Generator - Camera Streaming</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28cde8 0%, #1e90ff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Main Screen Container */
        .main-screen {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            min-height: 600px;
        }

        .screen {
            display: none;
            padding: 40px;
        }

        .screen.active {
            display: block;
        }

        /* Camera Input Screen */
        .camera-input-screen {
            display: flex;
            flex-direction: row;
            gap: 20px;
            align-items: stretch;
            justify-content: space-between;
            flex-wrap: nowrap;
            height: auto;
            min-height: 600px;
        }

        .camera-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            flex: 1;
            width: 48%;
            min-width: 450px;
            max-width: none;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .camera-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .camera-subtitle {
            color: #666;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .camera-video {
            width: 100%;
            max-width: 500px;
            height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: #000;
            object-fit: cover;
        }

        .camera-controls {
            margin-top: 20px;
        }

        .focus-controls {
            margin: 15px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 10px;
        }

        .focus-label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #495057;
        }

        .focus-slider {
            width: 100%;
            height: 5px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            margin-bottom: 10px;
        }

        .focus-value {
            font-size: 0.9rem;
            color: #666;
        }

        .btn {
            background: linear-gradient(135deg, #28cde8 0%, #1e90ff 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 205, 232, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-capture {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            font-size: 1.1rem;
            padding: 15px 30px;
        }

        .btn-capture:hover {
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-generate {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            font-size: 1.2rem;
            padding: 18px 40px;
            margin-top: 20px;
        }

        .btn-generate:hover {
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }

        .capture-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }

        .capture-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .capture-status.pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .generate-section {
            width: 100%;
            text-align: center;
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }

        /* Results Screen */
        .results-screen {
            text-align: center;
        }

        .results-header {
            margin-bottom: 30px;
        }

        .results-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 15px;
        }

        .results-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .generated-images {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .image-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .result-image {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .card-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: left;
        }

        .info-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .info-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            display: block;
            margin-bottom: 5px;
        }

        .info-value {
            color: #333;
            font-size: 1.1rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #28cde8;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            color: #666;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .camera-input-screen {
                flex-direction: column;
                gap: 20px;
                flex-wrap: wrap;
            }

            .camera-section {
                width: 100%;
                min-width: 100%;
                max-width: 100%;
            }

            .results-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .screen {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .camera-video {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎯 AI Card Visit Generator</h1>
            <p>Tạo ảnh AI dollhouse từ business card và khuôn mặt với camera streaming trực tiếp</p>
        </div>

        <!-- Main Screen Container -->
        <div class="main-screen">
            <!-- Camera Input Screen -->
            <div id="cameraInputScreen" class="screen camera-input-screen active">
                <!-- Card Camera Section (Trái - Chụp trước) -->
                <div class="camera-section">
                    <h3 class="camera-title">📷 Camera Business Card (Bước 1)</h3>
                    <p class="camera-subtitle" id="cardCameraSubtitle">Camera 0 - Logitech C270 với manual focus</p>

                    <img id="cardVideoFeed" class="camera-video" src="/video_feed_card" alt="Card Camera Feed">

                    <!-- Focus Controls for Card Camera -->
                    <div class="focus-controls">
                        <label class="focus-label">🎯 Manual Focus (Text Clarity)</label>
                        <input type="range" id="focusSlider" class="focus-slider" min="0" max="255" value="0">
                        <div class="focus-value">Focus: <span id="focusValue">0</span></div>
                    </div>

                    <div class="camera-controls">
                        <button id="captureCardBtn" class="btn btn-capture">📸 Chụp Business Card</button>
                        <div id="cardStatus" class="capture-status pending">Bước 1: Chụp business card trước</div>
                    </div>
                </div>

                <!-- Face Camera Section (Phải - Chụp sau) -->
                <div class="camera-section">
                    <h3 class="camera-title">👤 Camera Khuôn Mặt (Bước 2)</h3>
                    <p class="camera-subtitle" id="faceCameraSubtitle">Camera 1 - Laptop camera cho khuôn mặt</p>

                    <img id="faceVideoFeed" class="camera-video" src="/video_feed_face" alt="Face Camera Feed">

                    <div class="camera-controls">
                        <button id="captureFaceBtn" class="btn btn-capture" disabled>📸 Chụp Khuôn Mặt</button>
                        <div id="faceStatus" class="capture-status pending">Bước 2: Chụp sau khi có business card</div>
                    </div>
                </div>

                <!-- Generate Section -->
                <div class="generate-section">
                    <h3>🚀 Tạo Ảnh AI Dollhouse</h3>
                    <p>Sau khi chụp xong cả 2 ảnh, nhấn nút bên dưới để bắt đầu xử lý OCR và tạo ảnh AI</p>
                    <button id="generateBtn" class="btn btn-generate" disabled>
                        ✨ Xử Lý OCR & Tạo Ảnh AI
                    </button>
                </div>
            </div>

            <!-- Loading Screen -->
            <div id="loadingScreen" class="screen loading">
                <div class="spinner"></div>
                <div class="loading-text">Đang xử lý...</div>
                <div class="loading-subtitle">
                    <span id="loadingStep">Đang khởi tạo camera...</span>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="resultsScreen" class="screen results-screen">
                <div class="results-header">
                    <h2 class="results-title">🎉 Kết Quả AI Generation</h2>
                    <button id="backToInputBtn" class="btn">🔄 Chụp Ảnh Mới</button>
                </div>

                <div class="results-content">
                    <!-- Generated Images -->
                    <div class="generated-images">
                        <h3>🖼️ Ảnh AI Dollhouse</h3>
                        <div id="imagesGrid" class="images-grid">
                            <!-- Images will be populated here -->
                        </div>
                    </div>

                    <!-- Card Information -->
                    <div class="card-info">
                        <h3 class="info-title">📋 Thông Tin Business Card</h3>
                        <div id="cardInfoDisplay">
                            <!-- Card info will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Messages -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            <div id="successMessage" class="success-message" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Global variables
        let cardCaptured = false;
        let faceCaptured = false;
        let cardFilepath = null;
        let faceFilepath = null;

        // DOM elements
        const cameraInputScreen = document.getElementById('cameraInputScreen');
        const loadingScreen = document.getElementById('loadingScreen');
        const resultsScreen = document.getElementById('resultsScreen');
        const loadingStep = document.getElementById('loadingStep');

        const focusSlider = document.getElementById('focusSlider');
        const focusValue = document.getElementById('focusValue');
        const captureCardBtn = document.getElementById('captureCardBtn');
        const captureFaceBtn = document.getElementById('captureFaceBtn');
        const generateBtn = document.getElementById('generateBtn');
        const backToInputBtn = document.getElementById('backToInputBtn');

        const cardStatus = document.getElementById('cardStatus');
        const faceStatus = document.getElementById('faceStatus');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initializeCameras();
            setupEventListeners();
            updateCameraInfo();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Focus control
            focusSlider.addEventListener('input', function() {
                const value = this.value;
                focusValue.textContent = value;
                adjustFocus(value);
            });

            // Capture buttons
            captureCardBtn.addEventListener('click', captureCardImage);
            captureFaceBtn.addEventListener('click', captureFaceImage);
            generateBtn.addEventListener('click', processAndGenerate);
            backToInputBtn.addEventListener('click', resetToInput);
        }

        // Initialize cameras
        async function initializeCameras() {
            showLoading('Đang khởi tạo camera...');

            try {
                const response = await fetch('/initialize_cameras');
                const result = await response.json();

                if (result.success) {
                    hideLoading();
                    showSuccess('Camera đã được khởi tạo thành công!');
                } else {
                    throw new Error('Không thể khởi tạo camera');
                }
            } catch (error) {
                hideLoading();
                showError('Lỗi khởi tạo camera: ' + error.message);
            }
        }

        // Update camera info based on camera status
        async function updateCameraInfo() {
            try {
                const response = await fetch('/camera_status');
                const status = await response.json();

                const cardSubtitle = document.getElementById('cardCameraSubtitle');
                const faceSubtitle = document.getElementById('faceCameraSubtitle');

                if (status.mode === 'single') {
                    cardSubtitle.textContent = `Camera 0 - Logitech (Card + Face)`;
                    faceSubtitle.textContent = `Camera 0 - Single mode`;

                    // Thêm thông báo single camera mode
                    const generateSection = document.querySelector('.generate-section h3');
                    generateSection.innerHTML = '🚀 Tạo Ảnh AI Dollhouse <small style="color: #666;">(Single Camera Mode)</small>';
                } else if (status.mode === 'dual') {
                    cardSubtitle.textContent = `Camera 0 - Logitech C270 với manual focus`;
                    faceSubtitle.textContent = `Camera 1 - Laptop camera cho khuôn mặt`;

                    const generateSection = document.querySelector('.generate-section h3');
                    generateSection.innerHTML = '🚀 Tạo Ảnh AI Dollhouse <small style="color: #666;">(Dual Camera Mode)</small>';
                }

                console.log('Camera status:', status);
            } catch (error) {
                console.error('Error getting camera status:', error);
            }
        }

        // Adjust focus for card camera
        async function adjustFocus(focusValue) {
            try {
                const response = await fetch('/adjust_focus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ focus_value: parseInt(focusValue) })
                });

                const result = await response.json();
                if (!result.success) {
                    console.warn('Focus adjustment failed');
                }
            } catch (error) {
                console.error('Error adjusting focus:', error);
            }
        }

        // Capture card image (Bước 1)
        async function captureCardImage() {
            captureCardBtn.disabled = true;
            cardStatus.textContent = 'Đang chụp business card...';
            cardStatus.className = 'capture-status pending';

            try {
                const response = await fetch('/capture_card', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    cardCaptured = true;
                    cardFilepath = result.filepath;
                    cardStatus.textContent = '✅ Bước 1 hoàn thành: Đã chụp business card';
                    cardStatus.className = 'capture-status success';

                    // Enable face capture button
                    captureFaceBtn.disabled = false;
                    faceStatus.textContent = 'Bước 2: Sẵn sàng chụp khuôn mặt';
                    faceStatus.className = 'capture-status pending';

                    showSuccess('✅ Đã chụp business card! Bây giờ hãy chụp khuôn mặt.');
                    checkGenerateReady();
                } else {
                    throw new Error(result.error || 'Không thể chụp ảnh business card');
                }
            } catch (error) {
                cardStatus.textContent = '❌ Lỗi chụp business card';
                cardStatus.className = 'capture-status pending';
                showError('Lỗi chụp business card: ' + error.message);
            } finally {
                captureCardBtn.disabled = false;
            }
        }

        // Capture face image (Bước 2)
        async function captureFaceImage() {
            if (!cardCaptured) {
                showError('Vui lòng chụp business card trước!');
                return;
            }

            captureFaceBtn.disabled = true;
            faceStatus.textContent = 'Đang chụp khuôn mặt...';
            faceStatus.className = 'capture-status pending';

            try {
                const response = await fetch('/capture_face', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    faceCaptured = true;
                    faceFilepath = result.filepath;
                    faceStatus.textContent = '✅ Bước 2 hoàn thành: Đã chụp khuôn mặt';
                    faceStatus.className = 'capture-status success';

                    showSuccess('✅ Đã chụp cả 2 ảnh! Sẵn sàng tạo ảnh AI.');
                    checkGenerateReady();
                } else {
                    throw new Error(result.error || 'Không thể chụp ảnh khuôn mặt');
                }
            } catch (error) {
                faceStatus.textContent = '❌ Lỗi chụp khuôn mặt';
                faceStatus.className = 'capture-status pending';
                showError('Lỗi chụp khuôn mặt: ' + error.message);
            } finally {
                captureFaceBtn.disabled = false;
            }
        }

        // Check if ready to generate
        function checkGenerateReady() {
            if (cardCaptured && faceCaptured) {
                generateBtn.disabled = false;
                generateBtn.textContent = '✨ Sẵn sàng tạo ảnh AI!';
            }
        }

        // Process OCR and generate AI images
        async function processAndGenerate() {
            if (!cardCaptured || !faceCaptured) {
                showError('Vui lòng chụp cả 2 ảnh trước khi tạo AI');
                return;
            }

            showLoading('Đang xử lý OCR business card...');
            generateBtn.disabled = true;

            try {
                // Step 1: Process OCR and generate AI
                loadingStep.textContent = 'Đang trích xuất thông tin từ business card...';

                const response = await fetch('/process_and_generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        card_filepath: cardFilepath,
                        face_filepath: faceFilepath
                    })
                });

                const result = await response.json();

                if (result.success) {
                    loadingStep.textContent = 'Hoàn thành! Đang hiển thị kết quả...';
                    setTimeout(() => {
                        hideLoading();
                        showResults(result);
                    }, 1000);
                } else {
                    throw new Error(result.error || 'Lỗi xử lý OCR và tạo ảnh AI');
                }
            } catch (error) {
                hideLoading();
                showError('Lỗi xử lý: ' + error.message);
                generateBtn.disabled = false;
            }
        }

        // Show results screen
        function showResults(data) {
            // Hide other screens
            cameraInputScreen.classList.remove('active');
            loadingScreen.classList.remove('active');
            resultsScreen.classList.add('active');

            // Display generated images
            const imagesGrid = document.getElementById('imagesGrid');
            imagesGrid.innerHTML = '';

            if (data.generated_images && data.generated_images.length > 0) {
                data.generated_images.forEach((imagePath, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';

                    const img = document.createElement('img');
                    img.src = `/outputs/${imagePath.split('/').pop()}`;
                    img.className = 'result-image';
                    img.alt = `AI Generated Image ${index + 1}`;

                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'btn';
                    downloadBtn.textContent = '💾 Tải xuống';
                    downloadBtn.onclick = () => downloadImage(imagePath);

                    imageItem.appendChild(img);
                    imageItem.appendChild(downloadBtn);
                    imagesGrid.appendChild(imageItem);
                });
            }

            // Display card information
            const cardInfoDisplay = document.getElementById('cardInfoDisplay');
            cardInfoDisplay.innerHTML = '';

            if (data.card_info) {
                Object.entries(data.card_info).forEach(([key, value]) => {
                    if (value && value.trim()) {
                        const infoItem = document.createElement('div');
                        infoItem.className = 'info-item';

                        const label = document.createElement('span');
                        label.className = 'info-label';
                        label.textContent = getVietnameseLabel(key);

                        const valueSpan = document.createElement('span');
                        valueSpan.className = 'info-value';
                        valueSpan.textContent = value;

                        infoItem.appendChild(label);
                        infoItem.appendChild(valueSpan);
                        cardInfoDisplay.appendChild(infoItem);
                    }
                });
            }
        }

        // Get Vietnamese labels for card info
        function getVietnameseLabel(key) {
            const labels = {
                'name': 'Họ và tên:',
                'company': 'Công ty:',
                'position': 'Chức vụ:',
                'phone': 'Điện thoại:',
                'email': 'Email:',
                'address': 'Địa chỉ:',
                'website': 'Website:'
            };
            return labels[key] || key + ':';
        }

        // Download image
        function downloadImage(imagePath) {
            const link = document.createElement('a');
            link.href = `/download/${imagePath.split('/').pop()}`;
            link.download = imagePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Reset to input screen
        function resetToInput() {
            // Reset states
            cardCaptured = false;
            faceCaptured = false;
            cardFilepath = null;
            faceFilepath = null;

            // Reset UI
            cardStatus.textContent = 'Chưa chụp ảnh';
            cardStatus.className = 'capture-status pending';
            faceStatus.textContent = 'Chưa chụp ảnh';
            faceStatus.className = 'capture-status pending';
            generateBtn.disabled = true;
            generateBtn.textContent = '✨ Xử Lý OCR & Tạo Ảnh AI';

            // Show input screen
            resultsScreen.classList.remove('active');
            cameraInputScreen.classList.add('active');

            hideMessages();
        }

        // Utility functions
        function showLoading(message) {
            loadingStep.textContent = message;
            cameraInputScreen.classList.remove('active');
            resultsScreen.classList.remove('active');
            loadingScreen.classList.add('active');
        }

        function hideLoading() {
            loadingScreen.classList.remove('active');
            cameraInputScreen.classList.add('active');
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            setTimeout(hideMessages, 5000);
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
            setTimeout(hideMessages, 3000);
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            // Cleanup if needed
        });
    </script>
</body>
</html>
