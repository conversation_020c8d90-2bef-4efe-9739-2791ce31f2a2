#!/usr/bin/env python3
"""
Test script để kiểm tra camera trước khi chạy server
"""

import cv2
import time

def test_camera(index, name):
    """Test một camera cụ thể"""
    print(f"\n🔍 Testing {name} (Camera {index})...")
    
    # Thử với DirectShow trước
    cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)
    
    if not cap.isOpened():
        print(f"❌ Could not open {name} with DirectShow")
        # Thử không có DirectShow
        cap = cv2.VideoCapture(index)
        if not cap.isOpened():
            print(f"❌ Could not open {name} at all")
            return False
        else:
            print(f"✅ {name} opened without DirectShow")
    else:
        print(f"✅ {name} opened with DirectShow")
    
    # Test đọc frame
    ret, frame = cap.read()
    if not ret:
        print(f"❌ Could not read frame from {name}")
        cap.release()
        return False
    
    print(f"✅ Successfully read frame from {name}")
    print(f"   Frame size: {frame.shape}")
    
    # Test đọc thêm vài frame
    for i in range(3):
        ret, frame = cap.read()
        if ret:
            print(f"   Frame {i+2}: OK")
        else:
            print(f"   Frame {i+2}: Failed")
            break
        time.sleep(0.1)
    
    cap.release()
    return True

def main():
    print("🚀 Camera Test Script")
    print("=" * 50)
    
    # Test camera 0 (thường là laptop camera)
    camera0_ok = test_camera(0, "Laptop Camera")
    
    # Test camera 1 (thường là USB camera)
    camera1_ok = test_camera(1, "USB Camera")
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Camera 0 (Laptop): {'✅ OK' if camera0_ok else '❌ Failed'}")
    print(f"Camera 1 (USB): {'✅ OK' if camera1_ok else '❌ Failed'}")
    
    if camera0_ok and camera1_ok:
        print("\n🎉 Both cameras are working! You can use dual camera mode.")
    elif camera0_ok:
        print("\n⚠️ Only camera 0 is working. Single camera mode will be used.")
    elif camera1_ok:
        print("\n⚠️ Only camera 1 is working. Will use camera 1 for both functions.")
    else:
        print("\n❌ No cameras are working! Please check your camera connections.")
    
    print("\n🚀 You can now run the server with: python run_server.py")

if __name__ == '__main__':
    main()
