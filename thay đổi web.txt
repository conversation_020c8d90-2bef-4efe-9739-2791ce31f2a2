hỗ trợ trả lời bằng tiếng việt , tôi muốn có sự thay đổi về web:
1, tôi muốn đổi giao di<PERSON> , ko phải là 5 giao diện cho các bước upload-extract- confirm-face-generate nữa, chỉ cần 2 màn hình giao diện thôi :
- màn hình 1, mình hình input, có 2 cam streaming trực tiếp , camera 1 là của camera logitech c270 (đã kết nối qua cổng usb, hãy code để kết nối và truy cập được cam) được dùng để chụp visit card, camera 2 của camera laptop như @d:\THUC TAP\AI_Image_And_Text/dukien.png . khi ấn chụp ảnh sẽ chụp camera 1 trước, sau đó đến cam 2. sau khi hoàn thành chụp ảnh ở 2 cam, sẽ có nút generate ảnh và web sẽ tiến hình ocr và gen ảnh
- sau khi ocr và gen ảnh tôi muốn kết quả sẽ như  @d:\THUC TAP\AI_Image_And_Text/khai.png 
2* những điều cần lưu ý:
- hãy làm thật chuẩn chỉ sao cho 2 camera được stream trực tiếp trên cùng 1 màn hình
- camera logitech c270 focus face tốt nhưng focus text thì rất kém @d:\THUC TAP\AI_Image_And_Text/thucte.png ,hãy tham khảo đoạn code dưới để tăng độ focus cho text lên mức chính xác nhất :
"Steps for Manual Focus with OpenCV:
Initialize Video Capture: Open the camera device using cv2.VideoCapture().
Python

    import cv2
    cap = cv2.VideoCapture(0) # 0 for default camera, or specify device index
Disable Autofocus: Turn off the camera's automatic focus.
Python

    cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
Set Focus Property: Adjust the focus value using cv2.CAP_PROP_FOCUS. The range of values depends on the specific camera model and driver, often requiring experimentation to find the optimal range.
Python

    focus_value = 100 # Example value, adjust as needed
    cap.set(cv2.CAP_PROP_FOCUS, focus_value)
Capture and Display Frames: Continuously capture frames and display them to visually assess the focus. You can implement a loop to allow for interactive adjustment of the focus value.
Python

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        cv2.imshow('Camera Feed', frame)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'): # Press 'q' to quit
            break
        # Example: Adjust focus with 'a' and 'd' keys
        elif key == ord('a'):
            focus_value = max(0, focus_value - 5)
            cap.set(cv2.CAP_PROP_FOCUS, focus_value)
            print(f"Focus: {focus_value}")
        elif key == ord('d'):
            focus_value = min(255, focus_value + 5) # Max value might differ
            cap.set(cv2.CAP_PROP_FOCUS, focus_value)
            print(f"Focus: {focus_value}")
Release Resources: Release the camera and destroy all OpenCV windows.
Python

    cap.release()
    cv2.destroyAllWindows()
"