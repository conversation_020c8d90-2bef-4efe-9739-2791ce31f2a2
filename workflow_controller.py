#!/usr/bin/env python3
"""
Workflow Controller for AI Card Visit Application
Manages the complete workflow from card upload to AI generation
"""

import os
import json
from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename

# Import services
from ai_generator import AII<PERSON>Generator
from gemini_ocr_service import Gemini<PERSON>RService
from camera_interface import CameraManager

class WorkflowController:
    """Controls the complete AI Card Visit workflow"""
    
    def __init__(self, mode="testing"):
        self.mode = mode  # "testing" or "production"
        self.current_session = {}
        
        # Initialize services
        print("🔧 Initializing Workflow Controller...")
        self.setup_services()
        
        print(f"✅ Workflow Controller ready in {mode} mode")
    
    def setup_services(self):
        """Initialize all required services"""
        try:
            # Initialize OCR service
            print("📝 Initializing OCR service...")
            self.ocr_service = GeminiOCRService()
            
            # Initialize AI generator
            print("🎨 Initializing AI generator...")
            self.ai_generator = AIImageGenerator()
            
            # Initialize camera manager
            print("📷 Initializing camera manager...")
            self.camera_manager = CameraManager(mode=self.mode)
            
            print("✅ All services initialized successfully")
            
        except Exception as e:
            print(f"❌ Service initialization error: {e}")
            raise
    
    def start_new_session(self):
        """Start a new workflow session"""
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.current_session = {
            'session_id': session_id,
            'card_path': None,
            'card_info': None,
            'face_path': None,
            'ai_result': None,
            'created_at': datetime.now().isoformat()
        }
        
        print(f"🆕 New session started: {session_id}")
        return session_id
    
    def save_captured_face(self, file, session_id):
        """Save captured face image"""
        try:
            if not file or file.filename == '':
                return None
            
            # Create session folder
            session_folder = Path('sessions') / session_id
            session_folder.mkdir(exist_ok=True)
            
            # Save face image
            face_path = session_folder / "face.jpg"
            file.save(str(face_path))
            
            # Update current session
            self.current_session['face_path'] = str(face_path)
            
            print(f"✅ Face saved: {face_path}")
            return str(face_path)
            
        except Exception as e:
            print(f"❌ Error saving face: {e}")
            return None
    
    def get_session_info(self):
        """Get current session information"""
        return {
            'session_id': self.current_session.get('session_id'),
            'has_card': bool(self.current_session.get('card_path')),
            'has_card_info': bool(self.current_session.get('card_info')),
            'has_face': bool(self.current_session.get('face_path')),
            'has_ai_result': bool(self.current_session.get('ai_result')),
            'mode': self.mode
        }
    
    def switch_to_production_mode(self):
        """Switch to production mode with external cameras"""
        self.mode = "production"
        
        # Reinitialize camera manager for production
        print("🔄 Switching to production mode...")
        self.camera_manager = CameraManager(mode="production")
        
        print("✅ Switched to production mode")
    
    def reset_session(self):
        """Reset current session"""
        self.current_session = {}
        print("🔄 Session reset")
